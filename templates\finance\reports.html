{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}التقارير المالية{% endblock %}

{% block extra_css %}
<style>
.report-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
}

.report-card {
    background: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.metric-card {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    margin-bottom: 20px;
    border-left: 4px solid #667eea;
}

.metric-value {
    font-size: 2rem;
    font-weight: bold;
    color: #667eea;
    margin-bottom: 5px;
}

.metric-label {
    color: #6c757d;
    font-size: 0.9rem;
}

.revenue-card {
    border-left-color: #38ef7d;
}

.revenue-card .metric-value {
    color: #38ef7d;
}

.expense-card {
    border-left-color: #ff6b6b;
}

.expense-card .metric-value {
    color: #ff6b6b;
}

.profit-card {
    border-left-color: #667eea;
}

.chart-container {
    background: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.filter-card {
    background: white;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.btn-report {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.btn-report:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    color: white;
}

.table-financial {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.progress-financial {
    height: 8px;
    border-radius: 4px;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="report-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h2 mb-2">📊 التقارير المالية</h1>
                <p class="mb-0 opacity-75">تحليل شامل للوضع المالي والأداء</p>
            </div>
            <div>
                <a href="{% url 'finance:dashboard' %}" class="btn btn-outline-light me-2">
                    <i class="fas fa-arrow-left"></i> العودة
                </a>
                <button class="btn btn-light" onclick="window.print()">
                    <i class="fas fa-print"></i> طباعة
                </button>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="filter-card">
        <form method="get" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">من تاريخ</label>
                <input type="date" name="date_from" class="form-control" value="{{ request.GET.date_from }}">
            </div>
            <div class="col-md-3">
                <label class="form-label">إلى تاريخ</label>
                <input type="date" name="date_to" class="form-control" value="{{ request.GET.date_to }}">
            </div>
            <div class="col-md-3">
                <label class="form-label">نوع التقرير</label>
                <select name="report_type" class="form-select">
                    <option value="summary">ملخص عام</option>
                    <option value="detailed">تفصيلي</option>
                    <option value="comparison">مقارنة</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div>
                    <button type="submit" class="btn btn-report">
                        <i class="fas fa-search"></i> تطبيق الفلاتر
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Key Metrics -->
    <div class="row">
        <div class="col-lg-3 col-md-6">
            <div class="metric-card revenue-card">
                <div class="metric-value">{{ revenue_data.total_revenue|floatformat:0 }}</div>
                <div class="metric-label">إجمالي الإيرادات (درهم)</div>
                <small class="text-muted">
                    <i class="fas fa-arrow-up text-success"></i>
                    +{{ revenue_data.monthly_revenue|floatformat:0 }} هذا الشهر
                </small>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="metric-card expense-card">
                <div class="metric-value">{{ expense_data.total_expenses|floatformat:0 }}</div>
                <div class="metric-label">إجمالي المصروفات (درهم)</div>
                <small class="text-muted">
                    <i class="fas fa-arrow-up text-warning"></i>
                    +{{ expense_data.monthly_expenses|floatformat:0 }} هذا الشهر
                </small>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="metric-card profit-card">
                <div class="metric-value">{{ profit_data.total_profit|floatformat:0 }}</div>
                <div class="metric-label">صافي الربح (درهم)</div>
                <small class="text-muted">
                    {% if profit_data.monthly_profit >= 0 %}
                    <i class="fas fa-arrow-up text-success"></i>
                    {% else %}
                    <i class="fas fa-arrow-down text-danger"></i>
                    {% endif %}
                    {{ profit_data.monthly_profit|floatformat:0 }} هذا الشهر
                </small>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="metric-card">
                <div class="metric-value">{{ revenue_data.outstanding_amount|floatformat:0 }}</div>
                <div class="metric-label">المبالغ المستحقة (درهم)</div>
                <small class="text-muted">
                    <i class="fas fa-clock text-warning"></i>
                    في انتظار التحصيل
                </small>
            </div>
        </div>
    </div>

    <!-- Charts -->
    <div class="row">
        <!-- Revenue vs Expenses Chart -->
        <div class="col-lg-8">
            <div class="chart-container">
                <h5 class="mb-4"><i class="fas fa-chart-line me-2"></i>الإيرادات مقابل المصروفات</h5>
                <canvas id="revenueExpenseChart" height="100"></canvas>
            </div>
        </div>

        <!-- Profit Margin -->
        <div class="col-lg-4">
            <div class="chart-container">
                <h5 class="mb-4"><i class="fas fa-chart-pie me-2"></i>هامش الربح</h5>
                <canvas id="profitMarginChart" height="200"></canvas>
                
                <div class="mt-4">
                    <div class="d-flex justify-content-between mb-2">
                        <span>هامش الربح:</span>
                        <strong>
                            {% widthratio profit_data.total_profit revenue_data.total_revenue 100 %}%
                        </strong>
                    </div>
                    <div class="progress progress-financial">
                        <div class="progress-bar bg-success" 
                             style="width: {% widthratio profit_data.total_profit revenue_data.total_revenue 100 %}%">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Tables -->
    <div class="row">
        <!-- Revenue Breakdown -->
        <div class="col-lg-6">
            <div class="table-financial">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th colspan="3" class="text-center bg-primary text-white">
                                    <i class="fas fa-arrow-up me-2"></i>تفصيل الإيرادات
                                </th>
                            </tr>
                            <tr>
                                <th>المصدر</th>
                                <th class="text-end">المبلغ</th>
                                <th class="text-end">النسبة</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>الفواتير المدفوعة</td>
                                <td class="text-end">{{ revenue_data.total_revenue|floatformat:0 }} درهم</td>
                                <td class="text-end">100%</td>
                            </tr>
                            <tr>
                                <td>الإيرادات الشهرية</td>
                                <td class="text-end">{{ revenue_data.monthly_revenue|floatformat:0 }} درهم</td>
                                <td class="text-end">
                                    {% widthratio revenue_data.monthly_revenue revenue_data.total_revenue 100 %}%
                                </td>
                            </tr>
                            <tr class="table-warning">
                                <td>المبالغ المعلقة</td>
                                <td class="text-end">{{ revenue_data.outstanding_amount|floatformat:0 }} درهم</td>
                                <td class="text-end">
                                    {% widthratio revenue_data.outstanding_amount revenue_data.total_revenue 100 %}%
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Expense Breakdown -->
        <div class="col-lg-6">
            <div class="table-financial">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th colspan="3" class="text-center bg-danger text-white">
                                    <i class="fas fa-arrow-down me-2"></i>تفصيل المصروفات
                                </th>
                            </tr>
                            <tr>
                                <th>الفئة</th>
                                <th class="text-end">المبلغ</th>
                                <th class="text-end">النسبة</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>إجمالي المصروفات</td>
                                <td class="text-end">{{ expense_data.total_expenses|floatformat:0 }} درهم</td>
                                <td class="text-end">100%</td>
                            </tr>
                            <tr>
                                <td>المصروفات الشهرية</td>
                                <td class="text-end">{{ expense_data.monthly_expenses|floatformat:0 }} درهم</td>
                                <td class="text-end">
                                    {% widthratio expense_data.monthly_expenses expense_data.total_expenses 100 %}%
                                </td>
                            </tr>
                            <tr class="table-warning">
                                <td>المصروفات المعلقة</td>
                                <td class="text-end">{{ expense_data.pending_expenses|floatformat:0 }} درهم</td>
                                <td class="text-end">
                                    {% widthratio expense_data.pending_expenses expense_data.total_expenses 100 %}%
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="report-card">
                <h5 class="mb-4"><i class="fas fa-clipboard-list me-2"></i>ملخص التقرير</h5>
                <div class="row">
                    <div class="col-md-4">
                        <h6 class="text-success">نقاط القوة</h6>
                        <ul class="list-unstyled">
                            {% if profit_data.total_profit > 0 %}
                            <li><i class="fas fa-check text-success me-2"></i>تحقيق أرباح إيجابية</li>
                            {% endif %}
                            {% if revenue_data.monthly_revenue > 0 %}
                            <li><i class="fas fa-check text-success me-2"></i>إيرادات شهرية مستقرة</li>
                            {% endif %}
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <h6 class="text-warning">نقاط تحتاج انتباه</h6>
                        <ul class="list-unstyled">
                            {% if revenue_data.outstanding_amount > 0 %}
                            <li><i class="fas fa-exclamation-triangle text-warning me-2"></i>مبالغ مستحقة التحصيل</li>
                            {% endif %}
                            {% if expense_data.pending_expenses > 0 %}
                            <li><i class="fas fa-exclamation-triangle text-warning me-2"></i>مصروفات معلقة</li>
                            {% endif %}
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <h6 class="text-info">التوصيات</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-lightbulb text-info me-2"></i>متابعة تحصيل المستحقات</li>
                            <li><i class="fas fa-lightbulb text-info me-2"></i>مراجعة المصروفات الشهرية</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Revenue vs Expenses Chart
const revenueExpenseCtx = document.getElementById('revenueExpenseChart').getContext('2d');
const revenueExpenseChart = new Chart(revenueExpenseCtx, {
    type: 'line',
    data: {
        labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
        datasets: [{
            label: 'الإيرادات',
            data: [12000, 15000, 18000, 14000, 16000, 20000],
            borderColor: '#38ef7d',
            backgroundColor: 'rgba(56, 239, 125, 0.1)',
            tension: 0.4,
            fill: true
        }, {
            label: 'المصروفات',
            data: [8000, 9000, 11000, 10000, 12000, 13000],
            borderColor: '#ff6b6b',
            backgroundColor: 'rgba(255, 107, 107, 0.1)',
            tension: 0.4,
            fill: true
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'top',
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return value.toLocaleString() + ' درهم';
                    }
                }
            }
        }
    }
});

// Profit Margin Chart
const profitMarginCtx = document.getElementById('profitMarginChart').getContext('2d');
const profitMarginChart = new Chart(profitMarginCtx, {
    type: 'doughnut',
    data: {
        labels: ['الأرباح', 'المصروفات'],
        datasets: [{
            data: [{{ profit_data.total_profit }}, {{ expense_data.total_expenses }}],
            backgroundColor: ['#38ef7d', '#ff6b6b'],
            borderWidth: 0
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});
</script>
{% endblock %}
