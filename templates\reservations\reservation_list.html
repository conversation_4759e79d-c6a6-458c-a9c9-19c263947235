{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}قائمة الحجوزات{% endblock %}

{% block extra_css %}
<style>
.reservation-card {
    background: white;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    border-left: 4px solid #667eea;
    transition: all 0.3s ease;
}

.reservation-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.reservation-card.confirmed {
    border-left-color: #38ef7d;
}

.reservation-card.pending {
    border-left-color: #fdbb2d;
}

.reservation-card.cancelled {
    border-left-color: #ff6b6b;
    background: linear-gradient(135deg, rgba(255,107,107,0.05) 0%, rgba(255,107,107,0.02) 100%);
}

.filter-card {
    background: white;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.btn-reservation {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    transition: all 0.3s ease;
}

.btn-reservation:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    color: white;
}

.status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-confirmed {
    background: #d1edff;
    color: #0c63e4;
}

.status-cancelled {
    background: #f8d7da;
    color: #721c24;
}

.payment-badge {
    padding: 4px 8px;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 500;
}

.payment-paid {
    background: #d4edda;
    color: #155724;
}

.payment-partial {
    background: #fff3cd;
    color: #856404;
}

.payment-pending {
    background: #f8d7da;
    color: #721c24;
}

.package-info {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border-radius: 10px;
    padding: 10px;
    margin-bottom: 10px;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">📅 قائمة الحجوزات</h1>
                    <p class="text-muted">إدارة جميع الحجوزات والرحلات</p>
                </div>
                <div>
                    <a href="{% url 'reservations:dashboard' %}" class="btn btn-outline-primary me-2">
                        <i class="fas fa-arrow-left"></i> العودة للوحة التحكم
                    </a>
                    <a href="{% url 'reservations:calendar' %}" class="btn btn-outline-info me-2">
                        <i class="fas fa-calendar"></i> التقويم
                    </a>
                    <button class="btn btn-reservation">
                        <i class="fas fa-plus"></i> حجز جديد
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="filter-card">
                <form method="get" class="row g-3">
                    <div class="col-md-2">
                        <label class="form-label">الحالة</label>
                        <select name="status" class="form-select">
                            <option value="">جميع الحالات</option>
                            {% for value, label in status_choices %}
                            <option value="{{ value }}" {% if request.GET.status == value %}selected{% endif %}>
                                {{ label }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">حالة الدفع</label>
                        <select name="payment_status" class="form-select">
                            <option value="">جميع الحالات</option>
                            {% for value, label in payment_status_choices %}
                            <option value="{{ value }}" {% if request.GET.payment_status == value %}selected{% endif %}>
                                {{ label }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">من تاريخ</label>
                        <input type="date" name="date_from" class="form-control" value="{{ request.GET.date_from }}">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">إلى تاريخ</label>
                        <input type="date" name="date_to" class="form-control" value="{{ request.GET.date_to }}">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">بحث</label>
                        <div class="input-group">
                            <input type="text" name="search" class="form-control" 
                                   placeholder="رقم الحجز أو اسم العميل..." 
                                   value="{{ request.GET.search }}">
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Reservations List -->
    <div class="row">
        {% for reservation in reservations %}
        <div class="col-lg-6 col-md-12">
            <div class="reservation-card {{ reservation.status }}">
                <div class="d-flex justify-content-between align-items-start mb-3">
                    <div>
                        <h5 class="mb-1">{{ reservation.reservation_number }}</h5>
                        <p class="text-muted mb-0">{{ reservation.client.full_name_ar }}</p>
                    </div>
                    <div class="text-end">
                        <span class="status-badge status-{{ reservation.status }}">
                            {{ reservation.get_status_display }}
                        </span>
                        <br>
                        <small class="payment-badge payment-{{ reservation.payment_status }}">
                            {{ reservation.get_payment_status_display }}
                        </small>
                    </div>
                </div>

                <div class="package-info">
                    <h6 class="mb-1">{{ reservation.package.title_ar }}</h6>
                    <small class="text-muted">{{ reservation.package.category.name_ar }}</small>
                </div>

                <div class="row mb-3">
                    <div class="col-6">
                        <small class="text-muted">تاريخ المغادرة</small>
                        <p class="mb-0">{{ reservation.departure_date }}</p>
                    </div>
                    <div class="col-6">
                        <small class="text-muted">تاريخ العودة</small>
                        <p class="mb-0">{{ reservation.return_date|default:"غير محدد" }}</p>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-4">
                        <small class="text-muted">البالغين</small>
                        <p class="mb-0"><i class="fas fa-user"></i> {{ reservation.adults }}</p>
                    </div>
                    <div class="col-4">
                        <small class="text-muted">الأطفال</small>
                        <p class="mb-0"><i class="fas fa-child"></i> {{ reservation.children }}</p>
                    </div>
                    <div class="col-4">
                        <small class="text-muted">الرضع</small>
                        <p class="mb-0"><i class="fas fa-baby"></i> {{ reservation.infants }}</p>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-6">
                        <small class="text-muted">المبلغ الإجمالي</small>
                        <h4 class="mb-0 text-primary">{{ reservation.total_amount }} درهم</h4>
                    </div>
                    <div class="col-6">
                        <small class="text-muted">تاريخ الحجز</small>
                        <p class="mb-0">{{ reservation.booking_date }}</p>
                    </div>
                </div>

                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        {% if reservation.status == 'pending' %}
                        <button class="btn btn-sm btn-outline-success">
                            <i class="fas fa-check"></i> تأكيد
                        </button>
                        <button class="btn btn-sm btn-outline-danger">
                            <i class="fas fa-times"></i> إلغاء
                        </button>
                        {% elif reservation.status == 'confirmed' %}
                        <span class="badge bg-success">
                            <i class="fas fa-check-circle"></i> مؤكد
                        </span>
                        {% endif %}
                    </div>
                    <div>
                        <a href="{% url 'reservations:reservation_detail' reservation.pk %}" class="btn btn-sm btn-outline-info">
                            <i class="fas fa-eye"></i> عرض
                        </a>
                        <button class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-edit"></i> تعديل
                        </button>
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-calendar-times fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">لا توجد حجوزات</h4>
                <p class="text-muted">لم يتم العثور على أي حجوزات تطابق معايير البحث</p>
                <button class="btn btn-reservation">
                    <i class="fas fa-plus"></i> إنشاء حجز جديد
                </button>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if is_paginated %}
    <div class="row mt-4">
        <div class="col-12">
            <nav aria-label="صفحات الحجوزات">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">
                            <i class="fas fa-angle-double-left"></i>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">
                            <i class="fas fa-angle-left"></i>
                        </a>
                    </li>
                    {% endif %}

                    {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                    <li class="page-item active">
                        <span class="page-link">{{ num }}</span>
                    </li>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ num }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">
                            {{ num }}
                        </a>
                    </li>
                    {% endif %}
                    {% endfor %}

                    {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">
                            <i class="fas fa-angle-right"></i>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">
                            <i class="fas fa-angle-double-right"></i>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
    </div>
    {% endif %}

    <!-- Summary -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="filter-card">
                <div class="row text-center">
                    <div class="col-md-3">
                        <h5 class="text-primary">{{ page_obj.paginator.count }}</h5>
                        <p class="text-muted mb-0">إجمالي الحجوزات</p>
                    </div>
                    <div class="col-md-3">
                        <h5 class="text-success">{{ reservations|length }}</h5>
                        <p class="text-muted mb-0">في هذه الصفحة</p>
                    </div>
                    <div class="col-md-3">
                        <h5 class="text-info">{{ page_obj.paginator.num_pages }}</h5>
                        <p class="text-muted mb-0">عدد الصفحات</p>
                    </div>
                    <div class="col-md-3">
                        <h5 class="text-warning">{{ page_obj.number }}</h5>
                        <p class="text-muted mb-0">الصفحة الحالية</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-submit form on filter change
document.querySelectorAll('select').forEach(function(select) {
    select.addEventListener('change', function() {
        this.form.submit();
    });
});

// Clear filters
function clearFilters() {
    window.location.href = '{% url "reservations:reservation_list" %}';
}
</script>
{% endblock %}
