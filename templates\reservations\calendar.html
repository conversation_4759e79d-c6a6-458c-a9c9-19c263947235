{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}تقويم الحجوزات{% endblock %}

{% block extra_css %}
<style>
.calendar-container {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.calendar-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
}

.calendar-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
}

.calendar-day-header {
    background: #f8f9fa;
    padding: 15px 10px;
    text-align: center;
    font-weight: 600;
    color: #495057;
}

.calendar-day {
    background: white;
    min-height: 120px;
    padding: 10px;
    position: relative;
    border: 1px solid #e9ecef;
}

.calendar-day.other-month {
    background: #f8f9fa;
    color: #6c757d;
}

.calendar-day.today {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border: 2px solid #667eea;
}

.day-number {
    font-weight: 600;
    margin-bottom: 5px;
}

.reservation-item {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    margin-bottom: 2px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.reservation-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.reservation-item.confirmed {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
}

.reservation-item.pending {
    background: linear-gradient(135deg, #fdbb2d 0%, #22c1c3 100%);
}

.reservation-item.cancelled {
    background: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%);
}

.btn-calendar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    transition: all 0.3s ease;
}

.btn-calendar:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    color: white;
}

.legend {
    display: flex;
    gap: 20px;
    align-items: center;
    margin-top: 20px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.legend-color {
    width: 16px;
    height: 16px;
    border-radius: 8px;
}

.stats-card {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    text-align: center;
}

.modal-content {
    border-radius: 15px;
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px 15px 0 0;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="calendar-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 mb-2">📅 تقويم الحجوزات</h1>
                <p class="mb-0 opacity-75">عرض جميع الرحلات والحجوزات حسب التاريخ</p>
            </div>
            <div>
                <a href="{% url 'reservations:dashboard' %}" class="btn btn-outline-light me-2">
                    <i class="fas fa-arrow-left"></i> العودة
                </a>
                <button class="btn btn-light">
                    <i class="fas fa-plus"></i> حجز جديد
                </button>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Calendar -->
        <div class="col-lg-9">
            <div class="calendar-container">
                <!-- Calendar Navigation -->
                <div class="calendar-nav">
                    <div>
                        <a href="?month={{ month|add:'-1' }}&year={{ year }}" class="btn btn-outline-primary">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                        <span class="mx-3 h5">
                            {% if month == 1 %}يناير{% elif month == 2 %}فبراير{% elif month == 3 %}مارس{% elif month == 4 %}أبريل{% elif month == 5 %}مايو{% elif month == 6 %}يونيو{% elif month == 7 %}يوليو{% elif month == 8 %}أغسطس{% elif month == 9 %}سبتمبر{% elif month == 10 %}أكتوبر{% elif month == 11 %}نوفمبر{% elif month == 12 %}ديسمبر{% endif %}
                            {{ year }}
                        </span>
                        <a href="?month={{ month|add:'1' }}&year={{ year }}" class="btn btn-outline-primary">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </div>
                    <div>
                        <a href="{% url 'reservations:calendar' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-home"></i> اليوم
                        </a>
                    </div>
                </div>

                <!-- Calendar Grid -->
                <div class="calendar-grid">
                    <!-- Day Headers -->
                    <div class="calendar-day-header">الأحد</div>
                    <div class="calendar-day-header">الاثنين</div>
                    <div class="calendar-day-header">الثلاثاء</div>
                    <div class="calendar-day-header">الأربعاء</div>
                    <div class="calendar-day-header">الخميس</div>
                    <div class="calendar-day-header">الجمعة</div>
                    <div class="calendar-day-header">السبت</div>

                    <!-- Calendar Days -->
                    {% for week in calendar_weeks %}
                        {% for day in week %}
                        <div class="calendar-day {% if day.is_other_month %}other-month{% endif %} {% if day.is_today %}today{% endif %}">
                            <div class="day-number">{{ day.day }}</div>
                            
                            {% for reservation in day.reservations %}
                            <div class="reservation-item {{ reservation.status }}" 
                                 data-bs-toggle="modal" 
                                 data-bs-target="#reservationModal"
                                 data-reservation-id="{{ reservation.id }}"
                                 title="{{ reservation.client.full_name_ar }} - {{ reservation.package.title_ar }}">
                                <div class="fw-bold">{{ reservation.reservation_number }}</div>
                                <div>{{ reservation.client.full_name_ar|truncatechars:15 }}</div>
                            </div>
                            {% endfor %}
                        </div>
                        {% endfor %}
                    {% endfor %}
                </div>

                <!-- Legend -->
                <div class="legend">
                    <div class="legend-item">
                        <div class="legend-color" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);"></div>
                        <span>مؤكدة</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: linear-gradient(135deg, #fdbb2d 0%, #22c1c3 100%);"></div>
                        <span>معلقة</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%);"></div>
                        <span>ملغية</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-3">
            <!-- Monthly Stats -->
            <div class="stats-card mb-3">
                <h6 class="text-muted">إحصائيات الشهر</h6>
                <h3 class="text-primary">{{ monthly_stats.total_reservations }}</h3>
                <p class="mb-0">إجمالي الحجوزات</p>
            </div>

            <div class="stats-card mb-3">
                <h6 class="text-muted">الحجوزات المؤكدة</h6>
                <h3 class="text-success">{{ monthly_stats.confirmed_reservations }}</h3>
                <p class="mb-0">حجز مؤكد</p>
            </div>

            <div class="stats-card mb-3">
                <h6 class="text-muted">الإيرادات المتوقعة</h6>
                <h3 class="text-info">{{ monthly_stats.expected_revenue|floatformat:0 }}</h3>
                <p class="mb-0">درهم</p>
            </div>

            <!-- Quick Actions -->
            <div class="calendar-container">
                <h6 class="mb-3">إجراءات سريعة</h6>
                <div class="d-grid gap-2">
                    <button class="btn btn-calendar">
                        <i class="fas fa-plus me-2"></i>حجز جديد
                    </button>
                    <a href="{% url 'reservations:reservation_list' %}" class="btn btn-outline-primary">
                        <i class="fas fa-list me-2"></i>جميع الحجوزات
                    </a>
                    <a href="{% url 'reservations:reports' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-chart-bar me-2"></i>التقارير
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Reservation Modal -->
<div class="modal fade" id="reservationModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل الحجز</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="reservationDetails">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <a href="#" id="viewReservationBtn" class="btn btn-primary">عرض التفاصيل</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Calendar functionality
document.addEventListener('DOMContentLoaded', function() {
    // Handle reservation item clicks
    document.querySelectorAll('.reservation-item').forEach(item => {
        item.addEventListener('click', function() {
            const reservationId = this.dataset.reservationId;
            loadReservationDetails(reservationId);
        });
    });
});

function loadReservationDetails(reservationId) {
    const detailsContainer = document.getElementById('reservationDetails');
    const viewBtn = document.getElementById('viewReservationBtn');
    
    // Show loading
    detailsContainer.innerHTML = `
        <div class="text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
        </div>
    `;
    
    // Simulate API call (replace with actual API endpoint)
    setTimeout(() => {
        detailsContainer.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h6>معلومات العميل</h6>
                    <p><strong>الاسم:</strong> أحمد محمد</p>
                    <p><strong>البريد:</strong> <EMAIL></p>
                    <p><strong>الهاتف:</strong> +212 6 12 34 56 78</p>
                </div>
                <div class="col-md-6">
                    <h6>تفاصيل الرحلة</h6>
                    <p><strong>الباقة:</strong> رحلة مراكش</p>
                    <p><strong>تاريخ المغادرة:</strong> 2024-01-15</p>
                    <p><strong>المدة:</strong> 5 أيام</p>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-6">
                    <h6>المشاركين</h6>
                    <p><strong>البالغين:</strong> 2</p>
                    <p><strong>الأطفال:</strong> 1</p>
                </div>
                <div class="col-md-6">
                    <h6>المبلغ</h6>
                    <p><strong>الإجمالي:</strong> 5,000 درهم</p>
                    <p><strong>المدفوع:</strong> 2,000 درهم</p>
                </div>
            </div>
        `;
        
        viewBtn.href = `/reservations/${reservationId}/`;
    }, 1000);
}

// Generate calendar weeks (this would normally come from the backend)
function generateCalendarWeeks(year, month) {
    // This is a simplified version - the actual implementation would be in the Django view
    const weeks = [];
    // Implementation would go here
    return weeks;
}
</script>
{% endblock %}
