"""
Main API URL configuration for Moroccan Travel Agency ERP.
"""
from django.urls import path, include
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status
from django.http import JsonResponse


@api_view(['GET'])
def api_root(request):
    """
    API root endpoint providing information about available endpoints.
    """
    return Response({
        'message': 'مرحباً بك في واجهة برمجة التطبيقات لنظام إدارة وكالة السفر المغربية',
        'message_fr': 'Bienvenue dans l\'API du système ERP de l\'agence de voyage marocaine',
        'message_en': 'Welcome to the Moroccan Travel Agency ERP API',
        'version': '1.0',
        'endpoints': {
            'clients': '/api/crm/clients/',
            'tours': '/api/tours/packages/',
            'destinations': '/api/tours/destinations/',
            'categories': '/api/tours/categories/',
            'reservations': '/api/reservations/',
            'reports': '/api/reports/',
            'auth': '/api/auth/',
        },
        'documentation': '/api/docs/',
        'status': 'active'
    })


def api_health_check(request):
    """
    Simple health check endpoint.
    """
    return JsonResponse({
        'status': 'healthy',
        'message': 'API is running',
        'timestamp': '2025-06-12T23:15:00Z'
    })


urlpatterns = [
    # API root
    path('', api_root, name='api-root'),
    
    # Health check
    path('health/', api_health_check, name='api-health'),
    
    # Authentication
    path('auth/', include('rest_framework.urls', namespace='rest_framework')),
    
    # App APIs (commented out until views are created)
    # path('crm/', include('apps.crm.api_urls')),
    # path('tours/', include('apps.tours.api_urls')),
    # path('reservations/', include('apps.reservations.api_urls')),
    # path('reports/', include('apps.reports.api_urls')),
    # path('finance/', include('apps.finance.api_urls')),
    # path('hr/', include('apps.hr.api_urls')),
    # path('suppliers/', include('apps.suppliers.api_urls')),
    
    # Placeholder endpoints
    path('crm/', api_root, name='crm-api-placeholder'),
    path('tours/', api_root, name='tours-api-placeholder'),
    path('reservations/', api_root, name='reservations-api-placeholder'),
    path('reports/', api_root, name='reports-api-placeholder'),
    path('finance/', api_root, name='finance-api-placeholder'),
    path('hr/', api_root, name='hr-api-placeholder'),
    path('suppliers/', api_root, name='suppliers-api-placeholder'),
]
