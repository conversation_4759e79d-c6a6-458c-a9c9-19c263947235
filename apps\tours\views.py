"""
Views for Tours app.
"""
from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q, Count, Avg
from django.http import JsonResponse
from django.views.generic import ListView, DetailView, CreateView, UpdateView
from django.urls import reverse_lazy
from .models import TourPackage, Category, Destination
from .forms import TourPackageForm, TourPackageSearchForm


@login_required
def package_list(request):
    """List all tour packages with search and filtering."""
    packages = TourPackage.objects.all().select_related('category', 'destination')
    
    # Search functionality
    search_form = TourPackageSearchForm(request.GET)
    if search_form.is_valid():
        search_query = search_form.cleaned_data.get('search')
        category = search_form.cleaned_data.get('category')
        destination = search_form.cleaned_data.get('destination')
        is_active = search_form.cleaned_data.get('is_active')
        
        if search_query:
            packages = packages.filter(
                Q(title_ar__icontains=search_query) |
                Q(title_en__icontains=search_query) |
                Q(description_ar__icontains=search_query) |
                Q(description_en__icontains=search_query)
            )
        
        if category:
            packages = packages.filter(category=category)
        
        if destination:
            packages = packages.filter(destination=destination)
        
        if is_active is not None:
            packages = packages.filter(is_active=is_active)
    
    # Pagination
    paginator = Paginator(packages, 12)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'search_form': search_form,
        'total_packages': packages.count(),
    }
    
    return render(request, 'tours/package_list.html', context)


@login_required
def package_detail(request, pk):
    """Display package details."""
    package = get_object_or_404(TourPackage, pk=pk)
    
    # Get package statistics
    stats = {
        'total_reservations': package.reservations.count(),
        'confirmed_reservations': package.reservations.filter(status='confirmed').count(),
        'total_revenue': package.reservations.filter(status='confirmed').aggregate(
            total=models.Sum('total_amount')
        )['total'] or 0,
        'average_rating': package.reviews.aggregate(
            avg_rating=Avg('rating')
        )['avg_rating'] or 0,
        'last_booking': package.reservations.order_by('-booking_date').first(),
    }
    
    # Recent reservations
    recent_reservations = package.reservations.order_by('-booking_date')[:5]
    
    # Recent reviews
    recent_reviews = package.reviews.order_by('-created_at')[:5]
    
    context = {
        'package': package,
        'stats': stats,
        'recent_reservations': recent_reservations,
        'recent_reviews': recent_reviews,
    }
    
    return render(request, 'tours/package_detail.html', context)


@login_required
def package_create(request):
    """Create new tour package."""
    if request.method == 'POST':
        form = TourPackageForm(request.POST, request.FILES)
        if form.is_valid():
            package = form.save(commit=False)
            package.created_by = request.user
            package.save()
            messages.success(request, f'تم إنشاء الباقة {package.title_ar} بنجاح')
            return redirect('tours:package_detail', pk=package.pk)
    else:
        form = TourPackageForm()
    
    context = {
        'form': form,
        'title': 'إضافة باقة سياحية جديدة',
    }
    
    return render(request, 'tours/package_form.html', context)


@login_required
def package_update(request, pk):
    """Update tour package."""
    package = get_object_or_404(TourPackage, pk=pk)
    
    if request.method == 'POST':
        form = TourPackageForm(request.POST, request.FILES, instance=package)
        if form.is_valid():
            form.save()
            messages.success(request, f'تم تحديث الباقة {package.title_ar} بنجاح')
            return redirect('tours:package_detail', pk=package.pk)
    else:
        form = TourPackageForm(instance=package)
    
    context = {
        'form': form,
        'package': package,
        'title': f'تعديل الباقة {package.title_ar}',
    }
    
    return render(request, 'tours/package_form.html', context)


@login_required
def package_delete(request, pk):
    """Delete tour package."""
    package = get_object_or_404(TourPackage, pk=pk)
    
    if request.method == 'POST':
        package_name = package.title_ar
        package.delete()
        messages.success(request, f'تم حذف الباقة {package_name} بنجاح')
        return redirect('tours:package_list')
    
    context = {
        'package': package,
    }
    
    return render(request, 'tours/package_confirm_delete.html', context)


@login_required
def dashboard(request):
    """Tours Dashboard."""
    # Package statistics
    total_packages = TourPackage.objects.count()
    active_packages = TourPackage.objects.filter(is_active=True).count()
    featured_packages = TourPackage.objects.filter(is_featured=True).count()
    
    # Category breakdown
    category_stats = Category.objects.annotate(
        package_count=Count('packages')
    ).order_by('-package_count')
    
    # Destination breakdown
    destination_stats = Destination.objects.annotate(
        package_count=Count('packages')
    ).order_by('-package_count')[:5]
    
    # Recent packages
    recent_packages = TourPackage.objects.order_by('-created_at')[:10]
    
    # Popular packages (by reservations)
    popular_packages = TourPackage.objects.annotate(
        reservation_count=Count('reservations')
    ).order_by('-reservation_count')[:5]
    
    # Monthly package creation trend
    from django.db.models import TruncMonth
    monthly_packages = TourPackage.objects.annotate(
        month=TruncMonth('created_at')
    ).values('month').annotate(
        count=Count('id')
    ).order_by('month')
    
    context = {
        'total_packages': total_packages,
        'active_packages': active_packages,
        'featured_packages': featured_packages,
        'category_stats': category_stats,
        'destination_stats': destination_stats,
        'recent_packages': recent_packages,
        'popular_packages': popular_packages,
        'monthly_packages': monthly_packages,
    }
    
    return render(request, 'tours/dashboard.html', context)


@login_required
def package_search_ajax(request):
    """AJAX search for packages."""
    query = request.GET.get('q', '')
    
    if len(query) < 2:
        return JsonResponse({'results': []})
    
    packages = TourPackage.objects.filter(
        Q(title_ar__icontains=query) |
        Q(title_en__icontains=query) |
        Q(description_ar__icontains=query)
    ).filter(is_active=True)[:10]
    
    results = []
    for package in packages:
        results.append({
            'id': package.id,
            'text': f"{package.title_ar} - {package.duration} أيام",
            'price': str(package.price_per_person),
            'duration': package.duration,
            'category': package.category.name_ar if package.category else '',
        })
    
    return JsonResponse({'results': results})


@login_required
def package_toggle_active(request, pk):
    """Toggle package active status."""
    if request.method == 'POST':
        package = get_object_or_404(TourPackage, pk=pk)
        package.is_active = not package.is_active
        package.save()
        
        status = 'نشطة' if package.is_active else 'غير نشطة'
        messages.success(request, f'تم تغيير حالة الباقة {package.title_ar} إلى {status}')
        
        return JsonResponse({
            'success': True,
            'is_active': package.is_active,
            'message': f'تم تغيير حالة الباقة إلى {status}'
        })
    
    return JsonResponse({'success': False})


@login_required
def package_toggle_featured(request, pk):
    """Toggle package featured status."""
    if request.method == 'POST':
        package = get_object_or_404(TourPackage, pk=pk)
        package.is_featured = not package.is_featured
        package.save()
        
        status = 'مميزة' if package.is_featured else 'عادية'
        messages.success(request, f'تم تغيير حالة الباقة {package.title_ar} إلى {status}')
        
        return JsonResponse({
            'success': True,
            'is_featured': package.is_featured,
            'message': f'تم تغيير حالة الباقة إلى {status}'
        })
    
    return JsonResponse({'success': False})


# Class-based views
class PackageListView(ListView):
    """List view for packages."""
    model = TourPackage
    template_name = 'tours/package_list.html'
    context_object_name = 'packages'
    paginate_by = 12
    
    def get_queryset(self):
        return TourPackage.objects.select_related('category', 'destination').order_by('-created_at')


class PackageDetailView(DetailView):
    """Detail view for package."""
    model = TourPackage
    template_name = 'tours/package_detail.html'
    context_object_name = 'package'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        package = self.object
        
        # Add statistics
        context['stats'] = {
            'total_reservations': package.reservations.count(),
            'confirmed_reservations': package.reservations.filter(status='confirmed').count(),
            'average_rating': package.reviews.aggregate(avg_rating=Avg('rating'))['avg_rating'] or 0,
        }
        
        # Recent reservations and reviews
        context['recent_reservations'] = package.reservations.order_by('-booking_date')[:5]
        context['recent_reviews'] = package.reviews.order_by('-created_at')[:5]
        
        return context


class PackageCreateView(CreateView):
    """Create view for package."""
    model = TourPackage
    form_class = TourPackageForm
    template_name = 'tours/package_form.html'
    success_url = reverse_lazy('tours:package_list')
    
    def form_valid(self, form):
        form.instance.created_by = self.request.user
        messages.success(self.request, f'تم إنشاء الباقة {form.instance.title_ar} بنجاح')
        return super().form_valid(form)


class PackageUpdateView(UpdateView):
    """Update view for package."""
    model = TourPackage
    form_class = TourPackageForm
    template_name = 'tours/package_form.html'
    
    def get_success_url(self):
        return reverse_lazy('tours:package_detail', kwargs={'pk': self.object.pk})
    
    def form_valid(self, form):
        messages.success(self.request, f'تم تحديث الباقة {form.instance.title_ar} بنجاح')
        return super().form_valid(form)
