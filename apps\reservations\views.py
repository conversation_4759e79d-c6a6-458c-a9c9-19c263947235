"""
Reservations views for the Moroccan Travel Agency ERP system.
"""
from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Sum, Count, Q
from django.utils import timezone
from django.views.generic import ListView, DetailView, CreateView, UpdateView
from django.contrib.auth.mixins import LoginRequiredMixin
from datetime import datetime, timedelta
from .models import Reservation, ReservationParticipant, ReservationService, ReservationDocument


@login_required
def reservations_dashboard(request):
    """Reservations dashboard view."""
    today = timezone.now().date()
    current_month = today.replace(day=1)
    
    # Reservation statistics
    stats = {
        'total_reservations': Reservation.objects.count(),
        'confirmed_reservations': Reservation.objects.filter(status='confirmed').count(),
        'pending_reservations': Reservation.objects.filter(status='pending').count(),
        'cancelled_reservations': Reservation.objects.filter(status='cancelled').count(),
        'monthly_reservations': Reservation.objects.filter(
            booking_date__gte=current_month
        ).count(),
        'upcoming_departures': Reservation.objects.filter(
            departure_date__gte=today,
            departure_date__lte=today + timedelta(days=7),
            status='confirmed'
        ).count(),
        'total_revenue': Reservation.objects.filter(
            status='confirmed'
        ).aggregate(total=Sum('total_amount'))['total'] or 0,
        'total_participants': Reservation.objects.filter(
            status='confirmed'
        ).aggregate(
            adults=Sum('adults'),
            children=Sum('children'),
            infants=Sum('infants')
        ),
    }
    
    # Calculate total participants
    participants = stats['total_participants']
    stats['total_participants'] = (
        (participants['adults'] or 0) + 
        (participants['children'] or 0) + 
        (participants['infants'] or 0)
    )
    
    # Recent reservations
    recent_reservations = Reservation.objects.select_related(
        'client', 'package'
    ).order_by('-booking_date')[:5]
    
    # Upcoming departures
    upcoming_departures = Reservation.objects.filter(
        departure_date__gte=today,
        departure_date__lte=today + timedelta(days=14),
        status='confirmed'
    ).select_related('client', 'package').order_by('departure_date')[:10]
    
    # Payment status distribution
    payment_stats = Reservation.objects.values('payment_status').annotate(
        count=Count('id')
    ).order_by('payment_status')
    
    # Monthly booking trends
    monthly_data = []
    for i in range(6):
        month_start = (current_month - timedelta(days=30*i)).replace(day=1)
        month_end = (month_start + timedelta(days=32)).replace(day=1) - timedelta(days=1)
        bookings = Reservation.objects.filter(
            booking_date__gte=month_start,
            booking_date__lte=month_end
        ).count()
        revenue = Reservation.objects.filter(
            booking_date__gte=month_start,
            booking_date__lte=month_end,
            status='confirmed'
        ).aggregate(total=Sum('total_amount'))['total'] or 0
        monthly_data.append({
            'month': month_start.strftime('%Y-%m'),
            'bookings': bookings,
            'revenue': float(revenue)
        })
    
    context = {
        'stats': stats,
        'recent_reservations': recent_reservations,
        'upcoming_departures': upcoming_departures,
        'payment_stats': payment_stats,
        'monthly_data': list(reversed(monthly_data)),
    }
    
    return render(request, 'reservations/dashboard.html', context)


class ReservationListView(LoginRequiredMixin, ListView):
    """List view for reservations."""
    model = Reservation
    template_name = 'reservations/reservation_list.html'
    context_object_name = 'reservations'
    paginate_by = 20
    
    def get_queryset(self):
        queryset = Reservation.objects.select_related(
            'client', 'package', 'sales_agent'
        ).order_by('-booking_date')
        
        # Filter by status
        status = self.request.GET.get('status')
        if status:
            queryset = queryset.filter(status=status)
        
        # Filter by payment status
        payment_status = self.request.GET.get('payment_status')
        if payment_status:
            queryset = queryset.filter(payment_status=payment_status)
        
        # Filter by departure date range
        date_from = self.request.GET.get('date_from')
        date_to = self.request.GET.get('date_to')
        if date_from:
            queryset = queryset.filter(departure_date__gte=date_from)
        if date_to:
            queryset = queryset.filter(departure_date__lte=date_to)
        
        # Search
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(reservation_number__icontains=search) |
                Q(client__first_name_ar__icontains=search) |
                Q(client__last_name_ar__icontains=search) |
                Q(client__email__icontains=search) |
                Q(package__title_ar__icontains=search)
            )
        
        return queryset
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['status_choices'] = Reservation.STATUS_CHOICES
        context['payment_status_choices'] = Reservation.PAYMENT_STATUS_CHOICES
        return context


class ReservationDetailView(LoginRequiredMixin, DetailView):
    """Detail view for reservation."""
    model = Reservation
    template_name = 'reservations/reservation_detail.html'
    context_object_name = 'reservation'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['participants'] = self.object.participants.all().order_by('participant_type', 'last_name')
        context['services'] = self.object.services.all().order_by('service_date', 'name')
        context['documents'] = self.object.documents.all().order_by('-created_at')
        context['payments'] = self.object.payments.all().order_by('-payment_date')
        return context


@login_required
def reservation_calendar(request):
    """Calendar view for reservations."""
    today = timezone.now().date()
    
    # Get month and year from request
    month = int(request.GET.get('month', today.month))
    year = int(request.GET.get('year', today.year))
    
    # Calculate date range for the month
    month_start = datetime(year, month, 1).date()
    if month == 12:
        month_end = datetime(year + 1, 1, 1).date() - timedelta(days=1)
    else:
        month_end = datetime(year, month + 1, 1).date() - timedelta(days=1)
    
    # Get reservations for the month
    reservations = Reservation.objects.filter(
        departure_date__gte=month_start,
        departure_date__lte=month_end,
        status__in=['confirmed', 'pending']
    ).select_related('client', 'package').order_by('departure_date')
    
    # Group reservations by date
    reservations_by_date = {}
    for reservation in reservations:
        date_key = reservation.departure_date.strftime('%Y-%m-%d')
        if date_key not in reservations_by_date:
            reservations_by_date[date_key] = []
        reservations_by_date[date_key].append(reservation)
    
    context = {
        'month': month,
        'year': year,
        'month_start': month_start,
        'month_end': month_end,
        'reservations_by_date': reservations_by_date,
        'today': today,
    }
    
    return render(request, 'reservations/calendar.html', context)


@login_required
def reservation_reports(request):
    """Reservation reports view."""
    today = timezone.now().date()
    current_month = today.replace(day=1)
    
    # Booking statistics
    booking_stats = {
        'total_bookings': Reservation.objects.count(),
        'confirmed_bookings': Reservation.objects.filter(status='confirmed').count(),
        'cancelled_bookings': Reservation.objects.filter(status='cancelled').count(),
        'monthly_bookings': Reservation.objects.filter(
            booking_date__gte=current_month
        ).count(),
    }
    
    # Revenue statistics
    revenue_stats = {
        'total_revenue': Reservation.objects.filter(
            status='confirmed'
        ).aggregate(total=Sum('total_amount'))['total'] or 0,
        'monthly_revenue': Reservation.objects.filter(
            status='confirmed',
            booking_date__gte=current_month
        ).aggregate(total=Sum('total_amount'))['total'] or 0,
        'average_booking_value': Reservation.objects.filter(
            status='confirmed'
        ).aggregate(avg=Sum('total_amount'))['avg'] or 0,
    }
    
    # Popular packages
    popular_packages = Reservation.objects.filter(
        status='confirmed'
    ).values(
        'package__title_ar'
    ).annotate(
        booking_count=Count('id'),
        total_revenue=Sum('total_amount')
    ).order_by('-booking_count')[:10]
    
    # Booking sources
    booking_sources = Reservation.objects.values(
        'sales_agent__first_name',
        'sales_agent__last_name'
    ).annotate(
        booking_count=Count('id')
    ).order_by('-booking_count')[:10]
    
    context = {
        'booking_stats': booking_stats,
        'revenue_stats': revenue_stats,
        'popular_packages': popular_packages,
        'booking_sources': booking_sources,
    }
    
    return render(request, 'reservations/reports.html', context)
