{% load i18n %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% trans "تقارير الباقات" %} - نظام إدارة وكالة السفر المغربية</title>
    
    <!-- Bootstrap CSS RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Arabic Font -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        body { 
            font-family: 'Noto Sans Arabic', sans-serif; 
            background-color: #f8f9fa;
        }
        .report-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            text-align: center;
        }
        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }
        .price-card {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            color: white;
            border-radius: 10px;
            padding: 1rem;
            text-align: center;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-success">
        <div class="container-fluid">
            <a class="navbar-brand" href="/reports/">
                <i class="fas fa-map-marked-alt me-2"></i>
                {% trans "تقارير الباقات" %}
            </a>
            
            <div class="navbar-nav me-auto">
                <a class="nav-link" href="/">
                    <i class="fas fa-home me-1"></i>
                    {% trans "الرئيسية" %}
                </a>
                <a class="nav-link" href="/reports/">
                    <i class="fas fa-chart-bar me-1"></i>
                    {% trans "التقارير" %}
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid py-4">
        <!-- Header -->
        <div class="row">
            <div class="col-12">
                <h1 class="h3 mb-4">
                    <i class="fas fa-map-marked-alt me-2"></i>
                    {% trans "تقارير الباقات السياحية" %}
                </h1>
            </div>
        </div>

        <!-- Summary Cards -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="report-card">
                    <div class="h2">{{ total_packages }}</div>
                    <div>{% trans "إجمالي الباقات" %}</div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="report-card">
                    <div class="h2">{{ active_packages }}</div>
                    <div>{% trans "باقات نشطة" %}</div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="report-card">
                    <div class="h2">{{ featured_packages }}</div>
                    <div>{% trans "باقات مميزة" %}</div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="report-card">
                    <div class="h2">{{ inactive_packages }}</div>
                    <div>{% trans "باقات غير نشطة" %}</div>
                </div>
            </div>
        </div>

        <!-- Price Analysis -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="price-card">
                    <h4>{{ price_stats.avg_price|floatformat:0|default:"0" }} MAD</h4>
                    <small>{% trans "متوسط السعر" %}</small>
                </div>
            </div>
            <div class="col-md-4">
                <div class="price-card">
                    <h4>{{ price_stats.min_price|default:"0" }} MAD</h4>
                    <small>{% trans "أقل سعر" %}</small>
                </div>
            </div>
            <div class="col-md-4">
                <div class="price-card">
                    <h4>{{ price_stats.max_price|default:"0" }} MAD</h4>
                    <small>{% trans "أعلى سعر" %}</small>
                </div>
            </div>
        </div>

        <!-- Charts Row -->
        <div class="row mb-4">
            <!-- Category Distribution -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-tags me-2"></i>
                            {% trans "توزيع الباقات حسب الفئة" %}
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="categoryChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Difficulty Distribution -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-mountain me-2"></i>
                            {% trans "توزيع الباقات حسب الصعوبة" %}
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="difficultyChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Duration Analysis -->
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-calendar-alt me-2"></i>
                            {% trans "تحليل مدة الباقات" %}
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-4">
                                <h3 class="text-primary">{{ duration_stats.avg_duration|floatformat:1|default:"0" }}</h3>
                                <p>{% trans "متوسط المدة (أيام)" %}</p>
                            </div>
                            <div class="col-md-4">
                                <h3 class="text-success">{{ duration_stats.min_duration|default:"0" }}</h3>
                                <p>{% trans "أقصر مدة (أيام)" %}</p>
                            </div>
                            <div class="col-md-4">
                                <h3 class="text-warning">{{ duration_stats.max_duration|default:"0" }}</h3>
                                <p>{% trans "أطول مدة (أيام)" %}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Package Status -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">{% trans "حالة الباقات" %}</h6>
                    </div>
                    <div class="card-body">
                        <div class="chart-container" style="height: 200px;">
                            <canvas id="statusChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Packages -->
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-dollar-sign me-2"></i>
                            {% trans "أغلى الباقات" %}
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>{% trans "الباقة" %}</th>
                                        <th>{% trans "السعر" %}</th>
                                        <th>{% trans "المدة" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for package in most_expensive_packages %}
                                    <tr>
                                        <td>{{ package.title_ar }}</td>
                                        <td><strong>{{ package.base_price }} MAD</strong></td>
                                        <td>{{ package.duration_days }} {% trans "أيام" %}</td>
                                    </tr>
                                    {% empty %}
                                    <tr>
                                        <td colspan="3" class="text-center">{% trans "لا توجد بيانات" %}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-clock me-2"></i>
                            {% trans "أطول الباقات" %}
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>{% trans "الباقة" %}</th>
                                        <th>{% trans "المدة" %}</th>
                                        <th>{% trans "السعر" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for package in longest_packages %}
                                    <tr>
                                        <td>{{ package.title_ar }}</td>
                                        <td><strong>{{ package.duration_days }} {% trans "أيام" %}</strong></td>
                                        <td>{{ package.base_price }} MAD</td>
                                    </tr>
                                    {% empty %}
                                    <tr>
                                        <td colspan="3" class="text-center">{% trans "لا توجد بيانات" %}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Charts -->
    <script>
        // Category Chart
        const categoryCtx = document.getElementById('categoryChart').getContext('2d');
        new Chart(categoryCtx, {
            type: 'doughnut',
            data: {
                labels: [
                    {% for item in category_distribution %}
                        {% if item.category__name_ar %}'{{ item.category__name_ar }}'{% else %}'غير محدد'{% endif %}{% if not forloop.last %},{% endif %}
                    {% empty %}
                        'لا توجد بيانات'
                    {% endfor %}
                ],
                datasets: [{
                    data: [
                        {% for item in category_distribution %}
                            {{ item.count }}{% if not forloop.last %},{% endif %}
                        {% empty %}
                            0
                        {% endfor %}
                    ],
                    backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });

        // Difficulty Chart
        const difficultyCtx = document.getElementById('difficultyChart').getContext('2d');
        new Chart(difficultyCtx, {
            type: 'bar',
            data: {
                labels: [
                    {% for item in difficulty_distribution %}
                        '{{ item.difficulty_level|default:"غير محدد" }}'{% if not forloop.last %},{% endif %}
                    {% empty %}
                        'لا توجد بيانات'
                    {% endfor %}
                ],
                datasets: [{
                    label: 'عدد الباقات',
                    data: [
                        {% for item in difficulty_distribution %}
                            {{ item.count }}{% if not forloop.last %},{% endif %}
                        {% empty %}
                            0
                        {% endfor %}
                    ],
                    backgroundColor: '#36A2EB'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // Status Chart
        const statusCtx = document.getElementById('statusChart').getContext('2d');
        new Chart(statusCtx, {
            type: 'pie',
            data: {
                labels: ['نشطة', 'غير نشطة'],
                datasets: [{
                    data: [{{ active_packages }}, {{ inactive_packages }}],
                    backgroundColor: ['#28a745', '#dc3545']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });
    </script>
</body>
</html>
