"""
Simplified user and authentication models for the Moroccan Travel Agency ERP system.
"""
from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils.translation import gettext_lazy as _
from apps.core.models import TimeStampedModel


class User(AbstractUser):
    """Custom user model with additional fields for travel agency staff."""
    
    ROLE_CHOICES = [
        ('admin', _('مدير النظام')),
        ('manager', _('مدير الوكالة')),
        ('sales', _('موظف مبيعات')),
        ('accountant', _('محاسب')),
        ('guide', _('مرشد سياحي')),
        ('driver', _('سائق')),
        ('receptionist', _('موظف استقبال')),
    ]
    
    # Personal Information
    first_name_ar = models.CharField(_('الاسم الأول بالعربية'), max_length=50, blank=True)
    last_name_ar = models.CharField(_('اسم العائلة بالعربية'), max_length=50, blank=True)
    phone = models.CharField(_('رقم الهاتف'), max_length=20, blank=True)
    
    # Work Information
    role = models.CharField(_('الدور'), max_length=20, choices=ROLE_CHOICES, default='sales')
    employee_id = models.CharField(_('رقم الموظف'), max_length=20, unique=True, null=True, blank=True)
    
    # Preferences
    preferred_language = models.CharField(
        _('اللغة المفضلة'),
        max_length=5,
        choices=[('ar', 'العربية'), ('fr', 'Français'), ('en', 'English')],
        default='ar'
    )
    
    class Meta:
        verbose_name = _('مستخدم')
        verbose_name_plural = _('المستخدمون')
    
    def __str__(self):
        return f"{self.first_name_ar} {self.last_name_ar}" if self.first_name_ar else self.username
    
    @property
    def full_name_ar(self):
        """Return full name in Arabic."""
        return f"{self.first_name_ar} {self.last_name_ar}".strip()
    
    def get_role_display_ar(self):
        """Get role display in Arabic."""
        role_dict = dict(self.ROLE_CHOICES)
        return role_dict.get(self.role, self.role)
