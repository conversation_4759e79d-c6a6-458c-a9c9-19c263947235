"""
Task and project management models for the travel agency.
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.contenttypes.models import ContentType
from django.contrib.contenttypes.fields import GenericForeignKey
from apps.core.models import TimeStampedModel


class Project(TimeStampedModel):
    """Projects for organizing related tasks."""
    
    STATUS_CHOICES = [
        ('planning', _('تخطيط')),
        ('active', _('نشط')),
        ('on_hold', _('معلق')),
        ('completed', _('مكتمل')),
        ('cancelled', _('ملغي')),
    ]
    
    PRIORITY_CHOICES = [
        ('low', _('منخفض')),
        ('medium', _('متوسط')),
        ('high', _('عالي')),
        ('urgent', _('عاجل')),
    ]
    
    # Basic Information
    name = models.CharField(_('اسم المشروع'), max_length=200)
    description = models.TextField(_('الوصف'), blank=True)
    code = models.CharField(_('رمز المشروع'), max_length=20, unique=True)
    
    # Dates
    start_date = models.DateField(_('تاريخ البداية'), null=True, blank=True)
    end_date = models.DateField(_('تاريخ النهاية'), null=True, blank=True)
    deadline = models.DateField(_('الموعد النهائي'), null=True, blank=True)
    
    # Status and Priority
    status = models.CharField(_('الحالة'), max_length=20, choices=STATUS_CHOICES, default='planning')
    priority = models.CharField(_('الأولوية'), max_length=20, choices=PRIORITY_CHOICES, default='medium')
    
    # Team
    manager = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        related_name='managed_projects',
        verbose_name=_('مدير المشروع')
    )
    team_members = models.ManyToManyField(
        'accounts.User',
        through='ProjectMember',
        related_name='projects',
        verbose_name=_('أعضاء الفريق')
    )
    
    # Budget
    budget = models.DecimalField(_('الميزانية'), max_digits=12, decimal_places=2, null=True, blank=True)
    spent_amount = models.DecimalField(_('المبلغ المنفق'), max_digits=12, decimal_places=2, default=0)
    
    # Progress
    progress_percentage = models.PositiveIntegerField(_('نسبة الإنجاز'), default=0)
    
    # Related objects (generic foreign key)
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE, null=True, blank=True)
    object_id = models.PositiveIntegerField(null=True, blank=True)
    content_object = GenericForeignKey('content_type', 'object_id')
    
    notes = models.TextField(_('ملاحظات'), blank=True)
    
    class Meta:
        verbose_name = _('مشروع')
        verbose_name_plural = _('المشاريع')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['code']),
            models.Index(fields=['status', 'priority']),
            models.Index(fields=['manager']),
        ]
    
    def __str__(self):
        return f"{self.code} - {self.name}"
    
    @property
    def is_overdue(self):
        """Check if project is overdue."""
        if self.deadline and self.status not in ['completed', 'cancelled']:
            from django.utils import timezone
            return timezone.now().date() > self.deadline
        return False
    
    @property
    def remaining_budget(self):
        """Calculate remaining budget."""
        if self.budget:
            return self.budget - self.spent_amount
        return None
    
    def save(self, *args, **kwargs):
        if not self.code:
            # Generate project code
            last_project = Project.objects.filter(code__startswith='PRJ').order_by('-id').first()
            if last_project:
                last_number = int(last_project.code[3:])
                self.code = f"PRJ{last_number + 1:06d}"
            else:
                self.code = "PRJ000001"
        super().save(*args, **kwargs)


class ProjectMember(TimeStampedModel):
    """Project team members with roles."""
    
    ROLE_CHOICES = [
        ('member', _('عضو')),
        ('lead', _('قائد')),
        ('coordinator', _('منسق')),
        ('specialist', _('أخصائي')),
        ('consultant', _('استشاري')),
    ]
    
    project = models.ForeignKey(Project, on_delete=models.CASCADE, verbose_name=_('المشروع'))
    user = models.ForeignKey('accounts.User', on_delete=models.CASCADE, verbose_name=_('المستخدم'))
    role = models.CharField(_('الدور'), max_length=20, choices=ROLE_CHOICES, default='member')
    
    # Permissions
    can_create_tasks = models.BooleanField(_('يمكن إنشاء مهام'), default=True)
    can_assign_tasks = models.BooleanField(_('يمكن تعيين مهام'), default=False)
    can_edit_project = models.BooleanField(_('يمكن تعديل المشروع'), default=False)
    
    joined_date = models.DateField(_('تاريخ الانضمام'), auto_now_add=True)
    left_date = models.DateField(_('تاريخ المغادرة'), null=True, blank=True)
    
    is_active = models.BooleanField(_('نشط'), default=True)
    
    class Meta:
        verbose_name = _('عضو مشروع')
        verbose_name_plural = _('أعضاء المشاريع')
        unique_together = ['project', 'user']
    
    def __str__(self):
        return f"{self.project.name} - {self.user.username} ({self.get_role_display()})"


class Task(TimeStampedModel):
    """Individual tasks within projects."""
    
    STATUS_CHOICES = [
        ('todo', _('للقيام')),
        ('in_progress', _('قيد التنفيذ')),
        ('review', _('قيد المراجعة')),
        ('completed', _('مكتمل')),
        ('cancelled', _('ملغي')),
        ('blocked', _('محجوب')),
    ]
    
    PRIORITY_CHOICES = [
        ('low', _('منخفض')),
        ('medium', _('متوسط')),
        ('high', _('عالي')),
        ('urgent', _('عاجل')),
    ]
    
    # Basic Information
    title = models.CharField(_('عنوان المهمة'), max_length=200)
    description = models.TextField(_('الوصف'), blank=True)
    task_number = models.CharField(_('رقم المهمة'), max_length=20, unique=True)
    
    # Project Association
    project = models.ForeignKey(
        Project,
        on_delete=models.CASCADE,
        related_name='tasks',
        verbose_name=_('المشروع')
    )
    
    # Assignment
    assigned_to = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='assigned_tasks',
        verbose_name=_('مسند إلى')
    )
    created_by = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        related_name='created_tasks',
        verbose_name=_('أنشئ بواسطة')
    )
    
    # Dates
    start_date = models.DateField(_('تاريخ البداية'), null=True, blank=True)
    due_date = models.DateField(_('تاريخ الاستحقاق'), null=True, blank=True)
    completed_date = models.DateField(_('تاريخ الإنجاز'), null=True, blank=True)
    
    # Status and Priority
    status = models.CharField(_('الحالة'), max_length=20, choices=STATUS_CHOICES, default='todo')
    priority = models.CharField(_('الأولوية'), max_length=20, choices=PRIORITY_CHOICES, default='medium')
    
    # Progress
    progress_percentage = models.PositiveIntegerField(_('نسبة الإنجاز'), default=0)
    estimated_hours = models.DecimalField(_('الساعات المقدرة'), max_digits=6, decimal_places=2, null=True, blank=True)
    actual_hours = models.DecimalField(_('الساعات الفعلية'), max_digits=6, decimal_places=2, default=0)
    
    # Dependencies
    depends_on = models.ManyToManyField(
        'self',
        symmetrical=False,
        blank=True,
        verbose_name=_('يعتمد على')
    )
    
    # Tags and Labels
    tags = models.JSONField(_('العلامات'), default=list, blank=True)
    
    # Related objects (generic foreign key)
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE, null=True, blank=True)
    object_id = models.PositiveIntegerField(null=True, blank=True)
    content_object = GenericForeignKey('content_type', 'object_id')
    
    notes = models.TextField(_('ملاحظات'), blank=True)
    
    class Meta:
        verbose_name = _('مهمة')
        verbose_name_plural = _('المهام')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['task_number']),
            models.Index(fields=['project', 'status']),
            models.Index(fields=['assigned_to', 'status']),
            models.Index(fields=['due_date']),
        ]
    
    def __str__(self):
        return f"{self.task_number} - {self.title}"
    
    @property
    def is_overdue(self):
        """Check if task is overdue."""
        if self.due_date and self.status not in ['completed', 'cancelled']:
            from django.utils import timezone
            return timezone.now().date() > self.due_date
        return False
    
    @property
    def can_start(self):
        """Check if task can be started (all dependencies completed)."""
        return not self.depends_on.exclude(status='completed').exists()
    
    def save(self, *args, **kwargs):
        if not self.task_number:
            # Generate task number
            project_prefix = self.project.code if self.project else 'TSK'
            last_task = Task.objects.filter(
                task_number__startswith=f"{project_prefix}-"
            ).order_by('-id').first()
            
            if last_task:
                last_number = int(last_task.task_number.split('-')[1])
                self.task_number = f"{project_prefix}-{last_number + 1:04d}"
            else:
                self.task_number = f"{project_prefix}-0001"
        
        # Set completed date when status changes to completed
        if self.status == 'completed' and not self.completed_date:
            from django.utils import timezone
            self.completed_date = timezone.now().date()
            self.progress_percentage = 100
        
        super().save(*args, **kwargs)
