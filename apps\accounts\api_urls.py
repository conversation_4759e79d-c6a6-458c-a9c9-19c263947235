"""
API URL configuration for accounts app.
"""
from django.urls import path, include
from rest_framework.routers import Default<PERSON>outer
from . import api_views

router = DefaultRouter()
router.register(r'users', api_views.UserViewSet)
router.register(r'roles', api_views.RoleViewSet)
router.register(r'permissions', api_views.PermissionViewSet)
router.register(r'sessions', api_views.UserSessionViewSet)

urlpatterns = [
    path('', include(router.urls)),
    
    # Authentication
    path('auth/login/', api_views.LoginView.as_view(), name='api-login'),
    path('auth/logout/', api_views.LogoutView.as_view(), name='api-logout'),
    path('auth/refresh/', api_views.RefreshTokenView.as_view(), name='api-refresh'),
    path('auth/profile/', api_views.ProfileView.as_view(), name='api-profile'),
    
    # User Management
    path('users/<int:user_id>/roles/', api_views.UserRoleListView.as_view(), name='user-roles'),
    path('users/<int:user_id>/permissions/', api_views.UserPermissionListView.as_view(), name='user-permissions'),
    path('users/search/', api_views.UserSearchView.as_view(), name='user-search'),
    
    # Password Management
    path('password/change/', api_views.PasswordChangeView.as_view(), name='password-change'),
    path('password/reset/', api_views.PasswordResetView.as_view(), name='password-reset'),
    path('password/reset/confirm/', api_views.PasswordResetConfirmView.as_view(), name='password-reset-confirm'),
]
