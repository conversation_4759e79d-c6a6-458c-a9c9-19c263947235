{% extends 'base.html' %}
{% load i18n %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    .client-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 20px;
        color: white;
        padding: 30px;
        margin-bottom: 30px;
        position: relative;
        overflow: hidden;
    }
    .client-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 200%;
        height: 200%;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
        animation: float 20s infinite linear;
    }
    @keyframes float {
        0% { transform: translate(-50%, -50%) rotate(0deg); }
        100% { transform: translate(-50%, -50%) rotate(360deg); }
    }
    .client-avatar {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 3rem;
        font-weight: bold;
        margin-bottom: 20px;
        border: 4px solid rgba(255, 255, 255, 0.3);
    }
    .info-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        border: none;
        transition: transform 0.3s ease;
    }
    .info-card:hover {
        transform: translateY(-5px);
    }
    .info-card .card-header {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 15px 15px 0 0;
        border: none;
        padding: 20px;
    }
    .info-item {
        display: flex;
        align-items: center;
        padding: 15px 0;
        border-bottom: 1px solid #f8f9fa;
    }
    .info-item:last-child {
        border-bottom: none;
    }
    .info-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        margin-right: 15px;
        font-size: 1.1rem;
    }
    .info-content {
        flex: 1;
    }
    .info-label {
        font-size: 0.875rem;
        color: #6c757d;
        margin-bottom: 2px;
    }
    .info-value {
        font-weight: 600;
        color: #495057;
    }
    .action-btn {
        border-radius: 25px;
        padding: 10px 25px;
        font-weight: 500;
        transition: all 0.3s ease;
    }
    .action-btn:hover {
        transform: translateY(-2px);
    }
    .vip-badge {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        border: none;
        border-radius: 20px;
        padding: 8px 16px;
        font-weight: 600;
    }
    .status-badge {
        border-radius: 20px;
        padding: 6px 12px;
        font-size: 0.875rem;
        font-weight: 500;
    }
    .timeline {
        position: relative;
        padding-left: 30px;
    }
    .timeline::before {
        content: '';
        position: absolute;
        left: 15px;
        top: 0;
        bottom: 0;
        width: 2px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    .timeline-item {
        position: relative;
        margin-bottom: 20px;
        background: white;
        border-radius: 10px;
        padding: 15px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    .timeline-item::before {
        content: '';
        position: absolute;
        left: -22px;
        top: 20px;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: #667eea;
        border: 3px solid white;
        box-shadow: 0 0 0 2px #667eea;
    }
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }
    .stat-card {
        background: white;
        border-radius: 15px;
        padding: 20px;
        text-align: center;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        border: none;
    }
    .stat-number {
        font-size: 2rem;
        font-weight: bold;
        color: #667eea;
        margin-bottom: 5px;
    }
    .stat-label {
        color: #6c757d;
        font-size: 0.875rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item">
                <a href="{% url 'crm:dashboard' %}">
                    <i class="fas fa-home"></i>
                    {% trans "الرئيسية" %}
                </a>
            </li>
            <li class="breadcrumb-item">
                <a href="{% url 'crm:client_list' %}">{% trans "العملاء" %}</a>
            </li>
            <li class="breadcrumb-item active">{{ client.full_name_ar }}</li>
        </ol>
    </nav>

    <!-- Client Header -->
    <div class="client-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <div class="d-flex align-items-center">
                    <div class="client-avatar me-4">
                        {{ client.first_name_ar|first }}{{ client.last_name_ar|first }}
                    </div>
                    <div>
                        <h1 class="mb-2">{{ client.full_name_ar }}</h1>
                        <p class="mb-2 opacity-75">
                            <i class="fas fa-id-card me-2"></i>
                            {{ client.client_code }}
                        </p>
                        <div class="d-flex flex-wrap gap-2">
                            {% if client.vip_status %}
                            <span class="vip-badge">
                                <i class="fas fa-crown me-1"></i>
                                VIP
                            </span>
                            {% endif %}
                            <span class="status-badge bg-info">
                                {{ client.get_client_type_display }}
                            </span>
                            <span class="status-badge bg-success">
                                <i class="fas fa-check-circle me-1"></i>
                                {% trans "نشط" %}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4 text-end">
                <div class="d-flex flex-column gap-2">
                    <a href="{% url 'crm:client_edit' client.pk %}" class="btn btn-light action-btn">
                        <i class="fas fa-edit me-2"></i>
                        {% trans "تعديل البيانات" %}
                    </a>
                    <button class="btn btn-outline-light action-btn" onclick="printClient()">
                        <i class="fas fa-print me-2"></i>
                        {% trans "طباعة" %}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number">5</div>
            <div class="stat-label">{% trans "الحجوزات" %}</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">12,500</div>
            <div class="stat-label">{% trans "إجمالي المدفوعات (درهم)" %}</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">3</div>
            <div class="stat-label">{% trans "الرحلات المكتملة" %}</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ client.loyalty_points }}</div>
            <div class="stat-label">{% trans "نقاط الولاء" %}</div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="row">
        <!-- Personal Information -->
        <div class="col-lg-6 mb-4">
            <div class="info-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-user me-2 text-primary"></i>
                        {% trans "المعلومات الشخصية" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-signature"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">{% trans "الاسم بالعربية" %}</div>
                            <div class="info-value">{{ client.full_name_ar }}</div>
                        </div>
                    </div>
                    
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-font"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">{% trans "الاسم بالفرنسية" %}</div>
                            <div class="info-value">{{ client.full_name_fr|default:"-" }}</div>
                        </div>
                    </div>
                    
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-venus-mars"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">{% trans "الجنس" %}</div>
                            <div class="info-value">{{ client.get_gender_display|default:"-" }}</div>
                        </div>
                    </div>
                    
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-birthday-cake"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">{% trans "تاريخ الميلاد" %}</div>
                            <div class="info-value">{{ client.date_of_birth|default:"-" }}</div>
                        </div>
                    </div>
                    
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-flag"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">{% trans "الجنسية" %}</div>
                            <div class="info-value">{{ client.nationality|default:"-" }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contact Information -->
        <div class="col-lg-6 mb-4">
            <div class="info-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-address-book me-2 text-primary"></i>
                        {% trans "معلومات الاتصال" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">{% trans "البريد الإلكتروني" %}</div>
                            <div class="info-value">
                                <a href="mailto:{{ client.email }}">{{ client.email }}</a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-phone"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">{% trans "الهاتف" %}</div>
                            <div class="info-value">
                                <a href="tel:{{ client.phone }}">{{ client.phone }}</a>
                            </div>
                        </div>
                    </div>
                    
                    {% if client.whatsapp %}
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fab fa-whatsapp"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">{% trans "واتساب" %}</div>
                            <div class="info-value">
                                <a href="https://wa.me/{{ client.whatsapp }}" target="_blank">
                                    {{ client.whatsapp }}
                                </a>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    
                    {% if client.address %}
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">{% trans "العنوان" %}</div>
                            <div class="info-value">{{ client.address }}</div>
                        </div>
                    </div>
                    {% endif %}
                    
                    {% if client.city %}
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-city"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">{% trans "المدينة" %}</div>
                            <div class="info-value">{{ client.city.name_ar }}</div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Documents -->
        <div class="col-lg-6 mb-4">
            <div class="info-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-id-card me-2 text-primary"></i>
                        {% trans "الوثائق والهوية" %}
                    </h5>
                </div>
                <div class="card-body">
                    {% if client.passport_number %}
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-passport"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">{% trans "رقم جواز السفر" %}</div>
                            <div class="info-value">{{ client.passport_number }}</div>
                        </div>
                    </div>
                    {% endif %}
                    
                    {% if client.passport_expiry %}
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-calendar-times"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">{% trans "انتهاء جواز السفر" %}</div>
                            <div class="info-value">{{ client.passport_expiry }}</div>
                        </div>
                    </div>
                    {% endif %}
                    
                    {% if client.national_id %}
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-id-badge"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">{% trans "رقم الهوية الوطنية" %}</div>
                            <div class="info-value">{{ client.national_id }}</div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Additional Information -->
        <div class="col-lg-6 mb-4">
            <div class="info-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2 text-primary"></i>
                        {% trans "معلومات إضافية" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-language"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">{% trans "اللغة المفضلة" %}</div>
                            <div class="info-value">{{ client.get_preferred_language_display }}</div>
                        </div>
                    </div>
                    
                    {% if client.special_requirements %}
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-exclamation-circle"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">{% trans "متطلبات خاصة" %}</div>
                            <div class="info-value">{{ client.special_requirements }}</div>
                        </div>
                    </div>
                    {% endif %}
                    
                    {% if client.dietary_restrictions %}
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-utensils"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">{% trans "قيود غذائية" %}</div>
                            <div class="info-value">{{ client.dietary_restrictions }}</div>
                        </div>
                    </div>
                    {% endif %}
                    
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-calendar-plus"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">{% trans "تاريخ التسجيل" %}</div>
                            <div class="info-value">{{ client.created_at|date:"Y-m-d H:i" }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="row">
        <div class="col-12">
            <div class="info-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-history me-2 text-primary"></i>
                        {% trans "النشاط الأخير" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="mb-1">{% trans "تم إنشاء ملف العميل" %}</h6>
                                    <p class="text-muted mb-0">{% trans "تم إنشاء ملف العميل في النظام" %}</p>
                                </div>
                                <small class="text-muted">{{ client.created_at|date:"Y-m-d H:i" }}</small>
                            </div>
                        </div>
                        
                        <!-- Add more timeline items here for reservations, payments, etc. -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function printClient() {
    window.print();
}

// Add print styles
const printStyles = `
    @media print {
        .client-header::before { display: none; }
        .action-btn { display: none; }
        .breadcrumb { display: none; }
        .info-card { break-inside: avoid; }
    }
`;

const styleSheet = document.createElement("style");
styleSheet.type = "text/css";
styleSheet.innerText = printStyles;
document.head.appendChild(styleSheet);
</script>
{% endblock %}
