// Custom JavaScript for Moroccan Travel Agency ERP

$(document).ready(function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // Auto-hide alerts after 5 seconds
    $('.alert').each(function() {
        var alert = $(this);
        setTimeout(function() {
            alert.fadeOut('slow');
        }, 5000);
    });

    // Confirm delete actions
    $('.delete-confirm').on('click', function(e) {
        e.preventDefault();
        var url = $(this).attr('href');
        var itemName = $(this).data('item-name') || 'هذا العنصر';
        
        if (confirm('هل أنت متأكد من حذف ' + itemName + '؟')) {
            window.location.href = url;
        }
    });

    // Form validation enhancement
    $('form').on('submit', function() {
        var form = $(this);
        var submitBtn = form.find('button[type="submit"]');
        
        // Disable submit button to prevent double submission
        submitBtn.prop('disabled', true);
        submitBtn.html('<i class="fas fa-spinner fa-spin me-2"></i>جاري الحفظ...');
        
        // Re-enable after 3 seconds (in case of validation errors)
        setTimeout(function() {
            submitBtn.prop('disabled', false);
            submitBtn.html(submitBtn.data('original-text') || 'حفظ');
        }, 3000);
    });

    // Store original button text
    $('button[type="submit"]').each(function() {
        $(this).data('original-text', $(this).html());
    });

    // Search functionality
    $('#searchInput').on('keyup', function() {
        var value = $(this).val().toLowerCase();
        $('#searchResults tr').filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
        });
    });

    // Auto-format phone numbers
    $('input[type="tel"]').on('input', function() {
        var value = $(this).val().replace(/\D/g, '');
        if (value.startsWith('212')) {
            value = '+' + value;
        } else if (value.startsWith('0')) {
            value = '+212' + value.substring(1);
        }
        $(this).val(value);
    });

    // Date picker initialization (if using a date picker library)
    if (typeof flatpickr !== 'undefined') {
        flatpickr('.datepicker', {
            dateFormat: 'Y-m-d',
            locale: 'ar'
        });
    }

    // Client search autocomplete
    $('#clientSearch').on('input', function() {
        var query = $(this).val();
        if (query.length >= 2) {
            $.ajax({
                url: '/api/crm/search/',
                data: { q: query },
                success: function(data) {
                    var results = $('#clientSearchResults');
                    results.empty();
                    
                    if (data.results && data.results.length > 0) {
                        data.results.forEach(function(client) {
                            results.append(
                                '<div class="list-group-item list-group-item-action client-result" data-client-id="' + client.id + '">' +
                                '<strong>' + client.full_name_ar + '</strong><br>' +
                                '<small class="text-muted">' + client.email + ' - ' + client.phone + '</small>' +
                                '</div>'
                            );
                        });
                        results.show();
                    } else {
                        results.hide();
                    }
                }
            });
        } else {
            $('#clientSearchResults').hide();
        }
    });

    // Handle client selection from search results
    $(document).on('click', '.client-result', function() {
        var clientId = $(this).data('client-id');
        var clientName = $(this).find('strong').text();
        
        $('#selectedClientId').val(clientId);
        $('#clientSearch').val(clientName);
        $('#clientSearchResults').hide();
    });

    // Hide search results when clicking outside
    $(document).on('click', function(e) {
        if (!$(e.target).closest('#clientSearch, #clientSearchResults').length) {
            $('#clientSearchResults').hide();
        }
    });

    // Dynamic form fields
    $('.add-form-row').on('click', function() {
        var template = $(this).data('template');
        var container = $(this).data('container');
        var newRow = $(template).clone();
        
        // Update field names and IDs
        var index = $(container + ' .form-row').length;
        newRow.find('input, select, textarea').each(function() {
            var name = $(this).attr('name');
            var id = $(this).attr('id');
            
            if (name) {
                $(this).attr('name', name.replace('__prefix__', index));
            }
            if (id) {
                $(this).attr('id', id.replace('__prefix__', index));
            }
        });
        
        $(container).append(newRow);
    });

    // Remove form row
    $(document).on('click', '.remove-form-row', function() {
        $(this).closest('.form-row').remove();
    });

    // Print functionality
    $('.print-btn').on('click', function() {
        window.print();
    });

    // Export functionality
    $('.export-btn').on('click', function() {
        var format = $(this).data('format');
        var url = window.location.href;
        
        if (url.includes('?')) {
            url += '&export=' + format;
        } else {
            url += '?export=' + format;
        }
        
        window.location.href = url;
    });

    // Sidebar toggle (for future use)
    $('#sidebarToggle').on('click', function() {
        $('#sidebar').toggleClass('collapsed');
        $('#content').toggleClass('expanded');
    });

    // Real-time notifications (WebSocket - placeholder)
    function initializeNotifications() {
        // This would connect to WebSocket for real-time notifications
        // For now, we'll use polling
        setInterval(function() {
            $.ajax({
                url: '/api/notifications/',
                success: function(data) {
                    if (data.count > 0) {
                        $('#notificationBadge').text(data.count).show();
                    } else {
                        $('#notificationBadge').hide();
                    }
                }
            });
        }, 30000); // Check every 30 seconds
    }

    // Initialize notifications if user is authenticated
    if ($('body').hasClass('authenticated')) {
        initializeNotifications();
    }

    // Smooth scrolling for anchor links
    $('a[href^="#"]').on('click', function(e) {
        e.preventDefault();
        var target = $($(this).attr('href'));
        if (target.length) {
            $('html, body').animate({
                scrollTop: target.offset().top - 100
            }, 500);
        }
    });

    // Back to top button
    $(window).scroll(function() {
        if ($(this).scrollTop() > 100) {
            $('#backToTop').fadeIn();
        } else {
            $('#backToTop').fadeOut();
        }
    });

    $('#backToTop').on('click', function() {
        $('html, body').animate({scrollTop: 0}, 500);
    });

    // Loading overlay functions
    window.showLoading = function() {
        $('body').append('<div class="spinner-overlay"><div class="spinner-border spinner-border-custom text-primary" role="status"><span class="visually-hidden">جاري التحميل...</span></div></div>');
    };

    window.hideLoading = function() {
        $('.spinner-overlay').remove();
    };

    // AJAX setup for CSRF token
    $.ajaxSetup({
        beforeSend: function(xhr, settings) {
            if (!this.crossDomain) {
                xhr.setRequestHeader("X-CSRFToken", $('[name=csrfmiddlewaretoken]').val());
            }
        }
    });

    // Initialize any charts (placeholder for Chart.js)
    if (typeof Chart !== 'undefined') {
        // Initialize charts here
        initializeCharts();
    }

    function initializeCharts() {
        // Sales chart
        var salesCtx = document.getElementById('salesChart');
        if (salesCtx) {
            new Chart(salesCtx, {
                type: 'line',
                data: {
                    labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                    datasets: [{
                        label: 'المبيعات',
                        data: [12, 19, 3, 5, 2, 3],
                        borderColor: 'rgb(13, 110, 253)',
                        backgroundColor: 'rgba(13, 110, 253, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }
    }
});

// Utility functions
function formatCurrency(amount, currency = 'MAD') {
    return new Intl.NumberFormat('ar-MA', {
        style: 'currency',
        currency: currency
    }).format(amount);
}

function formatDate(date, locale = 'ar-MA') {
    return new Intl.DateTimeFormat(locale).format(new Date(date));
}

function showNotification(message, type = 'info') {
    var alertClass = 'alert-' + type;
    var alert = $('<div class="alert ' + alertClass + ' alert-dismissible fade show position-fixed" style="top: 20px; right: 20px; z-index: 9999;" role="alert">' +
        message +
        '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
        '</div>');
    
    $('body').append(alert);
    
    setTimeout(function() {
        alert.fadeOut('slow', function() {
            $(this).remove();
        });
    }, 5000);
}
