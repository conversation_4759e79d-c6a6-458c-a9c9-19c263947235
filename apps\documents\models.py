"""
Document and file management system for the travel agency.
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import FileExtensionValidator
from django.contrib.contenttypes.models import ContentType
from django.contrib.contenttypes.fields import GenericForeignKey
from apps.core.models import TimeStampedModel
import os


class DocumentCategory(TimeStampedModel):
    """Categories for organizing documents."""
    
    name_ar = models.CharField(_('الاسم بالعربية'), max_length=100)
    name_fr = models.CharField(_('الاسم بالفرنسية'), max_length=100, blank=True)
    name_en = models.CharField(_('الاسم بالإنجليزية'), max_length=100, blank=True)
    
    description = models.TextField(_('الوصف'), blank=True)
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, verbose_name=_('الفئة الأب'))
    
    # Settings
    allowed_extensions = models.JSONField(_('الامتدادات المسموحة'), default=list, blank=True)
    max_file_size_mb = models.PositiveIntegerField(_('الحد الأقصى لحجم الملف (ميجابايت)'), default=10)
    requires_approval = models.BooleanField(_('يتطلب موافقة'), default=False)
    
    # Access Control
    is_public = models.BooleanField(_('عام'), default=False)
    allowed_roles = models.JSONField(_('الأدوار المسموحة'), default=list, blank=True)
    
    is_active = models.BooleanField(_('نشط'), default=True)
    sort_order = models.PositiveIntegerField(_('ترتيب العرض'), default=0)
    
    class Meta:
        verbose_name = _('فئة الوثائق')
        verbose_name_plural = _('فئات الوثائق')
        ordering = ['sort_order', 'name_ar']
    
    def __str__(self):
        return self.name_ar


class Document(TimeStampedModel):
    """Document storage and management."""
    
    STATUS_CHOICES = [
        ('draft', _('مسودة')),
        ('pending_approval', _('في انتظار الموافقة')),
        ('approved', _('موافق عليه')),
        ('rejected', _('مرفوض')),
        ('archived', _('مؤرشف')),
        ('expired', _('منتهي الصلاحية')),
    ]
    
    ACCESS_LEVELS = [
        ('public', _('عام')),
        ('internal', _('داخلي')),
        ('confidential', _('سري')),
        ('restricted', _('مقيد')),
    ]
    
    # Basic Information
    title = models.CharField(_('العنوان'), max_length=200)
    description = models.TextField(_('الوصف'), blank=True)
    document_number = models.CharField(_('رقم الوثيقة'), max_length=50, unique=True)
    
    # File Information
    file = models.FileField(_('الملف'), upload_to='documents/')
    original_filename = models.CharField(_('اسم الملف الأصلي'), max_length=255)
    file_size = models.PositiveIntegerField(_('حجم الملف (بايت)'), default=0)
    file_type = models.CharField(_('نوع الملف'), max_length=50)
    
    # Organization
    category = models.ForeignKey(DocumentCategory, on_delete=models.CASCADE, verbose_name=_('الفئة'))
    tags = models.JSONField(_('العلامات'), default=list, blank=True)
    
    # Version Control
    version = models.CharField(_('الإصدار'), max_length=20, default='1.0')
    is_latest_version = models.BooleanField(_('أحدث إصدار'), default=True)
    parent_document = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='versions',
        verbose_name=_('الوثيقة الأصل')
    )
    
    # Status and Access
    status = models.CharField(_('الحالة'), max_length=20, choices=STATUS_CHOICES, default='draft')
    access_level = models.CharField(_('مستوى الوصول'), max_length=20, choices=ACCESS_LEVELS, default='internal')
    
    # Dates
    effective_date = models.DateField(_('تاريخ السريان'), null=True, blank=True)
    expiry_date = models.DateField(_('تاريخ انتهاء الصلاحية'), null=True, blank=True)
    
    # People
    uploaded_by = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        related_name='uploaded_documents',
        verbose_name=_('رفع بواسطة')
    )
    approved_by = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_documents',
        verbose_name=_('وافق عليه')
    )
    approval_date = models.DateTimeField(_('تاريخ الموافقة'), null=True, blank=True)
    
    # Related objects (generic foreign key)
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE, null=True, blank=True)
    object_id = models.PositiveIntegerField(null=True, blank=True)
    content_object = GenericForeignKey('content_type', 'object_id')
    
    # Tracking
    download_count = models.PositiveIntegerField(_('عدد التحميلات'), default=0)
    view_count = models.PositiveIntegerField(_('عدد المشاهدات'), default=0)
    
    # Security
    is_encrypted = models.BooleanField(_('مشفر'), default=False)
    password_protected = models.BooleanField(_('محمي بكلمة مرور'), default=False)
    
    notes = models.TextField(_('ملاحظات'), blank=True)
    
    class Meta:
        verbose_name = _('وثيقة')
        verbose_name_plural = _('الوثائق')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['document_number']),
            models.Index(fields=['category', 'status']),
            models.Index(fields=['uploaded_by']),
            models.Index(fields=['expiry_date']),
        ]
    
    def __str__(self):
        return f"{self.document_number} - {self.title}"
    
    @property
    def file_extension(self):
        """Get file extension."""
        return os.path.splitext(self.original_filename)[1].lower()
    
    @property
    def is_expired(self):
        """Check if document is expired."""
        if self.expiry_date:
            from django.utils import timezone
            return timezone.now().date() > self.expiry_date
        return False
    
    @property
    def file_size_mb(self):
        """Get file size in MB."""
        return round(self.file_size / (1024 * 1024), 2)
    
    def save(self, *args, **kwargs):
        if not self.document_number:
            # Generate document number
            last_doc = Document.objects.filter(document_number__startswith='DOC').order_by('-id').first()
            if last_doc:
                last_number = int(last_doc.document_number[3:])
                self.document_number = f"DOC{last_number + 1:06d}"
            else:
                self.document_number = "DOC000001"
        
        # Set file information
        if self.file:
            self.original_filename = self.file.name
            self.file_size = self.file.size
            self.file_type = self.file_extension
        
        super().save(*args, **kwargs)


class DocumentAccess(TimeStampedModel):
    """Track document access and downloads."""
    
    ACTION_CHOICES = [
        ('view', _('عرض')),
        ('download', _('تحميل')),
        ('print', _('طباعة')),
        ('share', _('مشاركة')),
        ('edit', _('تعديل')),
    ]
    
    document = models.ForeignKey(
        Document,
        on_delete=models.CASCADE,
        related_name='access_logs',
        verbose_name=_('الوثيقة')
    )
    
    user = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        verbose_name=_('المستخدم')
    )
    
    action = models.CharField(_('الإجراء'), max_length=20, choices=ACTION_CHOICES)
    ip_address = models.GenericIPAddressField(_('عنوان IP'), null=True, blank=True)
    user_agent = models.TextField(_('معلومات المتصفح'), blank=True)
    
    # Additional context
    session_id = models.CharField(_('معرف الجلسة'), max_length=40, blank=True)
    referrer = models.URLField(_('المرجع'), blank=True)
    
    class Meta:
        verbose_name = _('سجل وصول الوثيقة')
        verbose_name_plural = _('سجلات وصول الوثائق')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['document', 'action']),
            models.Index(fields=['user', 'action']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"{self.document.title} - {self.get_action_display()} - {self.user}"


class DocumentShare(TimeStampedModel):
    """Document sharing with external parties."""
    
    document = models.ForeignKey(
        Document,
        on_delete=models.CASCADE,
        related_name='shares',
        verbose_name=_('الوثيقة')
    )
    
    # Recipient information
    recipient_email = models.EmailField(_('بريد المستقبل'))
    recipient_name = models.CharField(_('اسم المستقبل'), max_length=100, blank=True)
    
    # Sharing details
    shared_by = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        verbose_name=_('شارك بواسطة')
    )
    
    # Access control
    access_token = models.CharField(_('رمز الوصول'), max_length=100, unique=True)
    expires_at = models.DateTimeField(_('ينتهي في'), null=True, blank=True)
    max_downloads = models.PositiveIntegerField(_('الحد الأقصى للتحميلات'), null=True, blank=True)
    download_count = models.PositiveIntegerField(_('عدد التحميلات'), default=0)
    
    # Permissions
    can_download = models.BooleanField(_('يمكن التحميل'), default=True)
    can_print = models.BooleanField(_('يمكن الطباعة'), default=True)
    requires_password = models.BooleanField(_('يتطلب كلمة مرور'), default=False)
    
    # Status
    is_active = models.BooleanField(_('نشط'), default=True)
    last_accessed = models.DateTimeField(_('آخر وصول'), null=True, blank=True)
    
    message = models.TextField(_('رسالة'), blank=True)
    
    class Meta:
        verbose_name = _('مشاركة وثيقة')
        verbose_name_plural = _('مشاركات الوثائق')
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.document.title} - {self.recipient_email}"
    
    @property
    def is_expired(self):
        """Check if share link is expired."""
        if self.expires_at:
            from django.utils import timezone
            return timezone.now() > self.expires_at
        return False
    
    @property
    def download_limit_reached(self):
        """Check if download limit is reached."""
        if self.max_downloads:
            return self.download_count >= self.max_downloads
        return False
    
    def save(self, *args, **kwargs):
        if not self.access_token:
            import uuid
            self.access_token = str(uuid.uuid4())
        super().save(*args, **kwargs)
