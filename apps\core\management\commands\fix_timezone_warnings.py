"""
Management command to fix timezone warnings by updating naive datetime fields.
"""
from django.core.management.base import BaseCommand
from django.utils import timezone
from django.db import transaction
from apps.crm.models import Client


class Command(BaseCommand):
    help = 'Fix timezone warnings by updating naive datetime fields'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🔧 إصلاح تحذيرات المنطقة الزمنية...'))
        
        with transaction.atomic():
            # Update clients with naive datetime
            clients_updated = 0
            for client in Client.objects.all():
                if client.created_at and timezone.is_naive(client.created_at):
                    client.created_at = timezone.make_aware(client.created_at)
                    client.save(update_fields=['created_at'])
                    clients_updated += 1
                    
                if client.updated_at and timezone.is_naive(client.updated_at):
                    client.updated_at = timezone.make_aware(client.updated_at)
                    client.save(update_fields=['updated_at'])
            
            self.stdout.write(
                self.style.SUCCESS(f'✅ تم إصلاح {clients_updated} عميل')
            )
        
        self.stdout.write(self.style.SUCCESS('✅ تم إصلاح جميع تحذيرات المنطقة الزمنية!'))
