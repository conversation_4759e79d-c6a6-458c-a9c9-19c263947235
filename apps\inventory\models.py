"""
Inventory and asset management models for the travel agency.
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator
from apps.core.models import TimeStampedModel


class AssetCategory(TimeStampedModel):
    """Categories for organizing assets."""
    
    name_ar = models.CharField(_('الاسم بالعربية'), max_length=100)
    name_fr = models.Char<PERSON><PERSON>(_('الاسم بالفرنسية'), max_length=100, blank=True)
    name_en = models.CharField(_('الاسم بالإنجليزية'), max_length=100, blank=True)
    
    description = models.TextField(_('الوصف'), blank=True)
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, verbose_name=_('الفئة الأب'))
    
    # Settings
    requires_maintenance = models.BooleanField(_('يتطلب صيانة'), default=False)
    depreciation_rate = models.DecimalField(_('معدل الاستهلاك السنوي'), max_digits=5, decimal_places=2, default=0)
    
    is_active = models.BooleanField(_('نشط'), default=True)
    sort_order = models.PositiveIntegerField(_('ترتيب العرض'), default=0)
    
    class Meta:
        verbose_name = _('فئة الأصول')
        verbose_name_plural = _('فئات الأصول')
        ordering = ['sort_order', 'name_ar']
    
    def __str__(self):
        return self.name_ar


class Asset(TimeStampedModel):
    """Company assets and equipment."""
    
    STATUS_CHOICES = [
        ('active', _('نشط')),
        ('maintenance', _('قيد الصيانة')),
        ('retired', _('متقاعد')),
        ('sold', _('مباع')),
        ('lost', _('مفقود')),
        ('damaged', _('تالف')),
    ]
    
    CONDITION_CHOICES = [
        ('excellent', _('ممتاز')),
        ('good', _('جيد')),
        ('fair', _('مقبول')),
        ('poor', _('سيء')),
        ('broken', _('معطل')),
    ]
    
    # Basic Information
    asset_code = models.CharField(_('رمز الأصل'), max_length=20, unique=True)
    name = models.CharField(_('اسم الأصل'), max_length=200)
    category = models.ForeignKey(AssetCategory, on_delete=models.CASCADE, verbose_name=_('الفئة'))
    
    # Details
    description = models.TextField(_('الوصف'), blank=True)
    brand = models.CharField(_('العلامة التجارية'), max_length=100, blank=True)
    model = models.CharField(_('الموديل'), max_length=100, blank=True)
    serial_number = models.CharField(_('الرقم التسلسلي'), max_length=100, blank=True)
    
    # Financial Information
    purchase_price = models.DecimalField(_('سعر الشراء'), max_digits=12, decimal_places=2)
    current_value = models.DecimalField(_('القيمة الحالية'), max_digits=12, decimal_places=2)
    purchase_date = models.DateField(_('تاريخ الشراء'))
    warranty_expiry = models.DateField(_('انتهاء الضمان'), null=True, blank=True)
    
    # Location and Assignment
    location = models.CharField(_('الموقع'), max_length=200, blank=True)
    assigned_to = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('مسند إلى')
    )
    
    # Status
    status = models.CharField(_('الحالة'), max_length=20, choices=STATUS_CHOICES, default='active')
    condition = models.CharField(_('الحالة الفيزيائية'), max_length=20, choices=CONDITION_CHOICES, default='good')
    
    # Maintenance
    last_maintenance = models.DateField(_('آخر صيانة'), null=True, blank=True)
    next_maintenance = models.DateField(_('الصيانة القادمة'), null=True, blank=True)
    maintenance_interval_days = models.PositiveIntegerField(_('فترة الصيانة (أيام)'), null=True, blank=True)
    
    # Documentation
    image = models.ImageField(_('صورة الأصل'), upload_to='assets/', blank=True)
    documents = models.JSONField(_('الوثائق'), default=list, blank=True)
    
    notes = models.TextField(_('ملاحظات'), blank=True)
    
    class Meta:
        verbose_name = _('أصل')
        verbose_name_plural = _('الأصول')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['asset_code']),
            models.Index(fields=['category', 'status']),
            models.Index(fields=['assigned_to']),
        ]
    
    def __str__(self):
        return f"{self.asset_code} - {self.name}"
    
    def save(self, *args, **kwargs):
        if not self.asset_code:
            # Generate asset code
            last_asset = Asset.objects.filter(asset_code__startswith='AST').order_by('-id').first()
            if last_asset:
                last_number = int(last_asset.asset_code[3:])
                self.asset_code = f"AST{last_number + 1:06d}"
            else:
                self.asset_code = "AST000001"
        super().save(*args, **kwargs)


class MaintenanceRecord(TimeStampedModel):
    """Maintenance history for assets."""
    
    MAINTENANCE_TYPES = [
        ('preventive', _('صيانة وقائية')),
        ('corrective', _('صيانة إصلاحية')),
        ('emergency', _('صيانة طارئة')),
        ('upgrade', _('ترقية')),
        ('inspection', _('فحص')),
    ]
    
    asset = models.ForeignKey(Asset, on_delete=models.CASCADE, related_name='maintenance_records', verbose_name=_('الأصل'))
    
    # Maintenance Details
    maintenance_type = models.CharField(_('نوع الصيانة'), max_length=20, choices=MAINTENANCE_TYPES)
    description = models.TextField(_('وصف الصيانة'))
    maintenance_date = models.DateField(_('تاريخ الصيانة'))
    
    # Personnel
    performed_by = models.CharField(_('نفذت بواسطة'), max_length=200)
    supervised_by = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('تحت إشراف')
    )
    
    # Financial
    cost = models.DecimalField(_('التكلفة'), max_digits=10, decimal_places=2, default=0)
    supplier = models.ForeignKey(
        'suppliers.Supplier',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('المورد')
    )
    
    # Parts and Materials
    parts_used = models.JSONField(_('القطع المستخدمة'), default=list, blank=True)
    
    # Results
    before_condition = models.CharField(_('الحالة قبل الصيانة'), max_length=20, choices=Asset.CONDITION_CHOICES)
    after_condition = models.CharField(_('الحالة بعد الصيانة'), max_length=20, choices=Asset.CONDITION_CHOICES)
    
    # Next maintenance
    next_maintenance_date = models.DateField(_('تاريخ الصيانة القادمة'), null=True, blank=True)
    
    notes = models.TextField(_('ملاحظات'), blank=True)
    
    class Meta:
        verbose_name = _('سجل صيانة')
        verbose_name_plural = _('سجلات الصيانة')
        ordering = ['-maintenance_date']
    
    def __str__(self):
        return f"{self.asset.name} - {self.get_maintenance_type_display()} - {self.maintenance_date}"


class InventoryItem(TimeStampedModel):
    """Consumable inventory items."""
    
    UNIT_CHOICES = [
        ('piece', _('قطعة')),
        ('box', _('صندوق')),
        ('pack', _('علبة')),
        ('bottle', _('زجاجة')),
        ('kg', _('كيلوغرام')),
        ('liter', _('لتر')),
        ('meter', _('متر')),
        ('set', _('طقم')),
    ]
    
    # Basic Information
    item_code = models.CharField(_('رمز الصنف'), max_length=20, unique=True)
    name = models.CharField(_('اسم الصنف'), max_length=200)
    category = models.ForeignKey(AssetCategory, on_delete=models.CASCADE, verbose_name=_('الفئة'))
    
    description = models.TextField(_('الوصف'), blank=True)
    unit = models.CharField(_('الوحدة'), max_length=20, choices=UNIT_CHOICES, default='piece')
    
    # Stock Information
    current_stock = models.DecimalField(_('المخزون الحالي'), max_digits=10, decimal_places=2, default=0)
    minimum_stock = models.DecimalField(_('الحد الأدنى للمخزون'), max_digits=10, decimal_places=2, default=0)
    maximum_stock = models.DecimalField(_('الحد الأقصى للمخزون'), max_digits=10, decimal_places=2, default=0)
    
    # Pricing
    unit_cost = models.DecimalField(_('تكلفة الوحدة'), max_digits=10, decimal_places=2, default=0)
    last_purchase_price = models.DecimalField(_('آخر سعر شراء'), max_digits=10, decimal_places=2, default=0)
    
    # Supplier
    primary_supplier = models.ForeignKey(
        'suppliers.Supplier',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('المورد الأساسي')
    )
    
    # Storage
    storage_location = models.CharField(_('موقع التخزين'), max_length=200, blank=True)
    
    # Status
    is_active = models.BooleanField(_('نشط'), default=True)
    
    notes = models.TextField(_('ملاحظات'), blank=True)
    
    class Meta:
        verbose_name = _('صنف مخزني')
        verbose_name_plural = _('الأصناف المخزنية')
        ordering = ['name']
        indexes = [
            models.Index(fields=['item_code']),
            models.Index(fields=['category']),
        ]
    
    def __str__(self):
        return f"{self.item_code} - {self.name}"
    
    @property
    def is_low_stock(self):
        """Check if item is below minimum stock level."""
        return self.current_stock <= self.minimum_stock
    
    @property
    def stock_value(self):
        """Calculate total value of current stock."""
        return self.current_stock * self.unit_cost
    
    def save(self, *args, **kwargs):
        if not self.item_code:
            # Generate item code
            last_item = InventoryItem.objects.filter(item_code__startswith='ITM').order_by('-id').first()
            if last_item:
                last_number = int(last_item.item_code[3:])
                self.item_code = f"ITM{last_number + 1:06d}"
            else:
                self.item_code = "ITM000001"
        super().save(*args, **kwargs)


class StockMovement(TimeStampedModel):
    """Track stock movements (in/out)."""
    
    MOVEMENT_TYPES = [
        ('purchase', _('شراء')),
        ('sale', _('بيع')),
        ('transfer', _('نقل')),
        ('adjustment', _('تسوية')),
        ('return', _('إرجاع')),
        ('damage', _('تلف')),
        ('loss', _('فقدان')),
        ('consumption', _('استهلاك')),
    ]
    
    item = models.ForeignKey(InventoryItem, on_delete=models.CASCADE, related_name='movements', verbose_name=_('الصنف'))
    
    # Movement Details
    movement_type = models.CharField(_('نوع الحركة'), max_length=20, choices=MOVEMENT_TYPES)
    quantity = models.DecimalField(_('الكمية'), max_digits=10, decimal_places=2, validators=[MinValueValidator(0)])
    unit_price = models.DecimalField(_('سعر الوحدة'), max_digits=10, decimal_places=2, default=0)
    
    # Direction (positive for in, negative for out)
    is_inbound = models.BooleanField(_('وارد'), default=True)
    
    # Reference
    reference_number = models.CharField(_('رقم المرجع'), max_length=50, blank=True)
    
    # Personnel
    performed_by = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        verbose_name=_('نفذ بواسطة')
    )
    
    # Stock levels after movement
    stock_before = models.DecimalField(_('المخزون قبل الحركة'), max_digits=10, decimal_places=2)
    stock_after = models.DecimalField(_('المخزون بعد الحركة'), max_digits=10, decimal_places=2)
    
    notes = models.TextField(_('ملاحظات'), blank=True)
    
    class Meta:
        verbose_name = _('حركة مخزون')
        verbose_name_plural = _('حركات المخزون')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['item', 'movement_type']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        direction = "وارد" if self.is_inbound else "صادر"
        return f"{self.item.name} - {direction} {self.quantity} - {self.created_at.date()}"
