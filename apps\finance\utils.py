"""
Utility functions for Finance app.
"""
from django.http import HttpResponse
from django.template.loader import get_template
from django.conf import settings
import os
from datetime import datetime
from decimal import Decimal


def generate_invoice_pdf(invoice):
    """
    Generate PDF for invoice.
    """
    try:
        from reportlab.lib.pagesizes import A4
        from reportlab.lib import colors
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib.units import inch
        from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
        from reportlab.pdfgen import canvas
        from reportlab.lib.enums import TA_CENTER, TA_RIGHT, TA_LEFT
        
        # Create response
        response = HttpResponse(content_type='application/pdf')
        response['Content-Disposition'] = f'attachment; filename="invoice_{invoice.invoice_number}.pdf"'
        
        # Create PDF document
        doc = SimpleDocTemplate(response, pagesize=A4)
        story = []
        
        # Styles
        styles = getSampleStyleSheet()
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=colors.HexColor('#667eea')
        )
        
        header_style = ParagraphStyle(
            'CustomHeader',
            parent=styles['Heading2'],
            fontSize=16,
            spaceAfter=12,
            textColor=colors.HexColor('#495057')
        )
        
        normal_style = ParagraphStyle(
            'CustomNormal',
            parent=styles['Normal'],
            fontSize=12,
            spaceAfter=6
        )
        
        # Company Header
        story.append(Paragraph("وكالة السفر المغربية", title_style))
        story.append(Paragraph("Moroccan Travel Agency", styles['Normal']))
        story.append(Spacer(1, 20))
        
        # Invoice Header
        story.append(Paragraph(f"فاتورة رقم: {invoice.invoice_number}", header_style))
        story.append(Spacer(1, 12))
        
        # Invoice Info Table
        invoice_data = [
            ['معلومات الفاتورة', ''],
            ['رقم الفاتورة:', invoice.invoice_number],
            ['تاريخ الإصدار:', invoice.issue_date.strftime('%Y-%m-%d')],
            ['تاريخ الاستحقاق:', invoice.due_date.strftime('%Y-%m-%d')],
            ['الحالة:', invoice.get_status_display()],
        ]
        
        client_data = [
            ['معلومات العميل', ''],
            ['الاسم:', invoice.client.full_name_ar],
            ['البريد الإلكتروني:', invoice.client.email],
            ['الهاتف:', getattr(invoice.client, 'phone', 'غير محدد')],
        ]
        
        # Create two-column layout for invoice and client info
        info_table = Table([
            [Table(invoice_data, colWidths=[2*inch, 2*inch]), 
             Table(client_data, colWidths=[2*inch, 2*inch])]
        ], colWidths=[4*inch, 4*inch])
        
        info_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#f8f9fa')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.HexColor('#495057')),
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.white),
            ('GRID', (0, 0), (-1, -1), 1, colors.HexColor('#dee2e6'))
        ]))
        
        story.append(info_table)
        story.append(Spacer(1, 20))
        
        # Invoice Items
        story.append(Paragraph("عناصر الفاتورة", header_style))
        
        # Items table header
        items_data = [
            ['الوصف', 'الكمية', 'سعر الوحدة', 'الإجمالي']
        ]
        
        # Add items
        for item in invoice.items.all():
            items_data.append([
                item.description,
                str(item.quantity),
                f"{item.unit_price} درهم",
                f"{item.total_price} درهم"
            ])
        
        # Create items table
        items_table = Table(items_data, colWidths=[3*inch, 1*inch, 1.5*inch, 1.5*inch])
        items_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#667eea')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.white),
            ('GRID', (0, 0), (-1, -1), 1, colors.HexColor('#dee2e6')),
            ('ALIGN', (1, 1), (-1, -1), 'RIGHT'),
        ]))
        
        story.append(items_table)
        story.append(Spacer(1, 20))
        
        # Totals
        totals_data = [
            ['المجموع الفرعي:', f"{invoice.subtotal} درهم"],
            [f'الضريبة ({invoice.tax_rate}%):', f"{invoice.tax_amount} درهم"],
        ]
        
        if invoice.discount_amount:
            totals_data.append(['الخصم:', f"{invoice.discount_amount} درهم"])
        
        totals_data.append(['المبلغ الإجمالي:', f"{invoice.total_amount} درهم"])
        
        totals_table = Table(totals_data, colWidths=[2*inch, 2*inch])
        totals_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
            ('FONTNAME', (0, -1), (-1, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, -1), (-1, -1), 14),
            ('BACKGROUND', (0, -1), (-1, -1), colors.HexColor('#f8f9fa')),
            ('TEXTCOLOR', (0, -1), (-1, -1), colors.HexColor('#667eea')),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
            ('TOPPADDING', (0, 0), (-1, -1), 8),
        ]))
        
        # Right align totals table
        totals_wrapper = Table([[totals_table]], colWidths=[8*inch])
        totals_wrapper.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
        ]))
        
        story.append(totals_wrapper)
        story.append(Spacer(1, 30))
        
        # Notes
        if invoice.notes:
            story.append(Paragraph("ملاحظات:", header_style))
            story.append(Paragraph(invoice.notes, normal_style))
            story.append(Spacer(1, 20))
        
        # Footer
        footer_text = """
        شكراً لتعاملكم معنا<br/>
        للاستفسارات: <EMAIL> | +212 5 22 XX XX XX<br/>
        العنوان: الدار البيضاء، المغرب
        """
        story.append(Paragraph(footer_text, styles['Normal']))
        
        # Build PDF
        doc.build(story)
        return response
        
    except ImportError:
        # Fallback if reportlab is not installed
        return generate_simple_invoice_pdf(invoice)


def generate_simple_invoice_pdf(invoice):
    """
    Generate simple PDF without reportlab (fallback).
    """
    from django.template.loader import render_to_string
    
    # Create HTML content
    html_content = render_to_string('finance/invoice_pdf.html', {
        'invoice': invoice,
        'items': invoice.items.all(),
        'company_name': 'وكالة السفر المغربية',
        'company_address': 'الدار البيضاء، المغرب',
        'company_phone': '+212 5 22 XX XX XX',
        'company_email': '<EMAIL>',
    })
    
    # Return HTML response (can be converted to PDF by browser)
    response = HttpResponse(html_content, content_type='text/html')
    response['Content-Disposition'] = f'attachment; filename="invoice_{invoice.invoice_number}.html"'
    return response


def send_invoice_email(invoice, recipient_email=None):
    """
    Send invoice via email.
    """
    from django.core.mail import EmailMessage
    from django.template.loader import render_to_string
    
    if not recipient_email:
        recipient_email = invoice.client.email
    
    # Email subject
    subject = f'فاتورة رقم {invoice.invoice_number} - وكالة السفر المغربية'
    
    # Email body
    email_body = render_to_string('finance/invoice_email.html', {
        'invoice': invoice,
        'client_name': invoice.client.full_name_ar,
    })
    
    # Create email
    email = EmailMessage(
        subject=subject,
        body=email_body,
        from_email=settings.DEFAULT_FROM_EMAIL,
        to=[recipient_email],
    )
    
    email.content_subtype = 'html'
    
    # Attach PDF
    try:
        pdf_response = generate_invoice_pdf(invoice)
        email.attach(
            f'invoice_{invoice.invoice_number}.pdf',
            pdf_response.content,
            'application/pdf'
        )
    except Exception as e:
        print(f"Error generating PDF attachment: {e}")
    
    # Send email
    try:
        email.send()
        return True
    except Exception as e:
        print(f"Error sending email: {e}")
        return False


def calculate_invoice_totals(subtotal, tax_rate=0, discount_amount=0):
    """
    Calculate invoice totals.
    """
    subtotal = Decimal(str(subtotal))
    tax_rate = Decimal(str(tax_rate))
    discount_amount = Decimal(str(discount_amount))
    
    tax_amount = (subtotal * tax_rate) / 100
    total_amount = subtotal + tax_amount - discount_amount
    
    return {
        'subtotal': subtotal,
        'tax_amount': tax_amount,
        'discount_amount': discount_amount,
        'total_amount': total_amount,
    }


def format_currency(amount, currency='MAD'):
    """
    Format currency amount.
    """
    if currency == 'MAD':
        return f"{amount:,.2f} درهم"
    else:
        return f"{amount:,.2f} {currency}"


def get_invoice_status_color(status):
    """
    Get color for invoice status.
    """
    colors = {
        'draft': '#6c757d',
        'sent': '#ffc107',
        'paid': '#28a745',
        'overdue': '#dc3545',
        'cancelled': '#6c757d',
    }
    return colors.get(status, '#6c757d')


def generate_invoice_number():
    """
    Generate unique invoice number.
    """
    from .models import Invoice
    
    today = datetime.now()
    prefix = f"INV-{today.year}{today.month:02d}"
    
    # Get last invoice number for this month
    last_invoice = Invoice.objects.filter(
        invoice_number__startswith=prefix
    ).order_by('-invoice_number').first()
    
    if last_invoice:
        # Extract sequence number and increment
        last_sequence = int(last_invoice.invoice_number.split('-')[-1])
        sequence = last_sequence + 1
    else:
        sequence = 1
    
    return f"{prefix}-{sequence:04d}"
