"""
Advanced notification system for the Moroccan Travel Agency ERP.
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.contenttypes.models import ContentType
from django.contrib.contenttypes.fields import GenericForeignKey
from apps.core.models import TimeStampedModel


class NotificationTemplate(TimeStampedModel):
    """Templates for different types of notifications."""
    
    NOTIFICATION_TYPES = [
        ('email', _('بريد إلكتروني')),
        ('sms', _('رسالة نصية')),
        ('whatsapp', _('واتساب')),
        ('push', _('إشعار فوري')),
        ('in_app', _('داخل التطبيق')),
    ]
    
    EVENT_TYPES = [
        ('reservation_confirmed', _('تأكيد الحجز')),
        ('reservation_cancelled', _('إلغاء الحجز')),
        ('payment_received', _('استلام دفعة')),
        ('payment_overdue', _('تأخر في الدفع')),
        ('visa_approved', _('موافقة على التأشيرة')),
        ('visa_rejected', _('رفض التأشيرة')),
        ('trip_reminder', _('تذكير بالرحلة')),
        ('document_required', _('وثيقة مطلوبة')),
        ('birthday_greeting', _('تهنئة عيد ميلاد')),
        ('marketing_offer', _('عرض تسويقي')),
        ('system_maintenance', _('صيانة النظام')),
    ]
    
    name = models.CharField(_('اسم القالب'), max_length=100)
    event_type = models.CharField(_('نوع الحدث'), max_length=30, choices=EVENT_TYPES)
    notification_type = models.CharField(_('نوع الإشعار'), max_length=20, choices=NOTIFICATION_TYPES)
    
    # Content in multiple languages
    subject_ar = models.CharField(_('الموضوع بالعربية'), max_length=200)
    subject_fr = models.CharField(_('الموضوع بالفرنسية'), max_length=200, blank=True)
    subject_en = models.CharField(_('الموضوع بالإنجليزية'), max_length=200, blank=True)
    
    content_ar = models.TextField(_('المحتوى بالعربية'))
    content_fr = models.TextField(_('المحتوى بالفرنسية'), blank=True)
    content_en = models.TextField(_('المحتوى بالإنجليزية'), blank=True)
    
    # Template variables (JSON format)
    variables = models.JSONField(_('المتغيرات'), default=dict, help_text=_('متغيرات القالب بصيغة JSON'))
    
    # Settings
    is_active = models.BooleanField(_('نشط'), default=True)
    send_immediately = models.BooleanField(_('إرسال فوري'), default=True)
    delay_minutes = models.PositiveIntegerField(_('تأخير بالدقائق'), default=0)
    
    class Meta:
        verbose_name = _('قالب إشعار')
        verbose_name_plural = _('قوالب الإشعارات')
        unique_together = ['event_type', 'notification_type']
        ordering = ['event_type', 'notification_type']
    
    def __str__(self):
        return f"{self.name} - {self.get_notification_type_display()}"


class Notification(TimeStampedModel):
    """Individual notification instances."""
    
    STATUS_CHOICES = [
        ('pending', _('معلق')),
        ('sent', _('مرسل')),
        ('delivered', _('تم التسليم')),
        ('failed', _('فشل')),
        ('cancelled', _('ملغي')),
    ]
    
    template = models.ForeignKey(
        NotificationTemplate,
        on_delete=models.CASCADE,
        verbose_name=_('القالب')
    )
    
    # Recipient
    recipient_user = models.ForeignKey(
        'accounts.User',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name=_('المستخدم المستقبل')
    )
    recipient_client = models.ForeignKey(
        'crm.Client',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name=_('العميل المستقبل')
    )
    
    # Contact details (for non-users)
    recipient_email = models.EmailField(_('بريد المستقبل'), blank=True)
    recipient_phone = models.CharField(_('هاتف المستقبل'), max_length=20, blank=True)
    recipient_name = models.CharField(_('اسم المستقبل'), max_length=100, blank=True)
    
    # Content (can be customized per notification)
    subject = models.CharField(_('الموضوع'), max_length=200)
    content = models.TextField(_('المحتوى'))
    language = models.CharField(
        _('اللغة'),
        max_length=5,
        choices=[('ar', 'العربية'), ('fr', 'Français'), ('en', 'English')],
        default='ar'
    )
    
    # Related object (generic foreign key)
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE, null=True, blank=True)
    object_id = models.PositiveIntegerField(null=True, blank=True)
    content_object = GenericForeignKey('content_type', 'object_id')
    
    # Delivery information
    status = models.CharField(_('الحالة'), max_length=20, choices=STATUS_CHOICES, default='pending')
    scheduled_time = models.DateTimeField(_('وقت الجدولة'), null=True, blank=True)
    sent_time = models.DateTimeField(_('وقت الإرسال'), null=True, blank=True)
    delivered_time = models.DateTimeField(_('وقت التسليم'), null=True, blank=True)
    
    # Tracking
    attempts = models.PositiveIntegerField(_('المحاولات'), default=0)
    max_attempts = models.PositiveIntegerField(_('الحد الأقصى للمحاولات'), default=3)
    error_message = models.TextField(_('رسالة الخطأ'), blank=True)
    
    # External service response
    external_id = models.CharField(_('المعرف الخارجي'), max_length=100, blank=True)
    provider_response = models.JSONField(_('استجابة المزود'), default=dict, blank=True)
    
    class Meta:
        verbose_name = _('إشعار')
        verbose_name_plural = _('الإشعارات')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['status', 'scheduled_time']),
            models.Index(fields=['recipient_user', 'status']),
            models.Index(fields=['recipient_client', 'status']),
        ]
    
    def __str__(self):
        recipient = self.recipient_user or self.recipient_client or self.recipient_name
        return f"{self.template.name} - {recipient}"


class NotificationPreference(TimeStampedModel):
    """User preferences for notifications."""
    
    user = models.OneToOneField(
        'accounts.User',
        on_delete=models.CASCADE,
        related_name='notification_preferences',
        verbose_name=_('المستخدم')
    )
    
    # Email preferences
    email_enabled = models.BooleanField(_('البريد الإلكتروني مفعل'), default=True)
    email_reservations = models.BooleanField(_('إشعارات الحجز بالبريد'), default=True)
    email_payments = models.BooleanField(_('إشعارات الدفع بالبريد'), default=True)
    email_marketing = models.BooleanField(_('التسويق بالبريد'), default=False)
    
    # SMS preferences
    sms_enabled = models.BooleanField(_('الرسائل النصية مفعلة'), default=True)
    sms_reservations = models.BooleanField(_('إشعارات الحجز بالرسائل'), default=True)
    sms_payments = models.BooleanField(_('إشعارات الدفع بالرسائل'), default=False)
    sms_reminders = models.BooleanField(_('التذكيرات بالرسائل'), default=True)
    
    # WhatsApp preferences
    whatsapp_enabled = models.BooleanField(_('الواتساب مفعل'), default=True)
    whatsapp_reservations = models.BooleanField(_('إشعارات الحجز بالواتساب'), default=True)
    whatsapp_updates = models.BooleanField(_('التحديثات بالواتساب'), default=True)
    
    # Push notification preferences
    push_enabled = models.BooleanField(_('الإشعارات الفورية مفعلة'), default=True)
    push_reservations = models.BooleanField(_('إشعارات الحجز الفورية'), default=True)
    push_reminders = models.BooleanField(_('التذكيرات الفورية'), default=True)
    
    # Timing preferences
    quiet_hours_start = models.TimeField(_('بداية الساعات الهادئة'), null=True, blank=True)
    quiet_hours_end = models.TimeField(_('نهاية الساعات الهادئة'), null=True, blank=True)
    timezone = models.CharField(_('المنطقة الزمنية'), max_length=50, default='Africa/Casablanca')
    
    class Meta:
        verbose_name = _('تفضيلات الإشعارات')
        verbose_name_plural = _('تفضيلات الإشعارات')
    
    def __str__(self):
        return f"تفضيلات {self.user.username}"


class NotificationQueue(TimeStampedModel):
    """Queue for batch processing notifications."""
    
    notification = models.OneToOneField(
        Notification,
        on_delete=models.CASCADE,
        verbose_name=_('الإشعار')
    )
    
    priority = models.PositiveIntegerField(_('الأولوية'), default=5)  # 1 = highest, 10 = lowest
    scheduled_for = models.DateTimeField(_('مجدول لـ'))
    processing_started = models.DateTimeField(_('بدء المعالجة'), null=True, blank=True)
    processing_completed = models.DateTimeField(_('انتهاء المعالجة'), null=True, blank=True)
    
    # Worker information
    worker_id = models.CharField(_('معرف العامل'), max_length=100, blank=True)
    
    class Meta:
        verbose_name = _('طابور الإشعارات')
        verbose_name_plural = _('طوابير الإشعارات')
        ordering = ['priority', 'scheduled_for']
        indexes = [
            models.Index(fields=['priority', 'scheduled_for']),
            models.Index(fields=['processing_started']),
        ]
    
    def __str__(self):
        return f"طابور {self.notification.template.name}"
