"""
Simplified URL configuration for Moroccan Travel Agency ERP project.
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.views.generic import TemplateView

urlpatterns = [
    # Admin
    path('admin/', admin.site.urls),
    
    # Main app URLs
    path('', TemplateView.as_view(template_name='core/dashboard.html'), name='dashboard'),
    path('accounts/', include('apps.accounts.urls')),
    path('crm/', include('apps.crm.urls')),
    path('tours/', include('apps.tours.urls')),
    
    # API (simplified)
    path('api/', include('rest_framework.urls')),
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
