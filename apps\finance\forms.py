"""
Forms for Finance app.
"""
from django import forms
from django.utils.translation import gettext_lazy as _
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Row, Column, Submit, HTML, Div
from crispy_forms.bootstrap import FormActions
from .models import Invoice, Payment, Expense, BankAccount, InvoiceItem
from apps.crm.models import Client
from apps.reservations.models import Reservation
from apps.suppliers.models import Supplier


class InvoiceForm(forms.ModelForm):
    """Form for creating and editing invoices."""
    
    class Meta:
        model = Invoice
        fields = [
            'client', 'reservation', 'issue_date', 'due_date',
            'subtotal', 'tax_rate', 'tax_amount', 'discount_amount',
            'total_amount', 'notes', 'status'
        ]
        widgets = {
            'issue_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'due_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'notes': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
            'subtotal': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
            'tax_rate': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
            'tax_amount': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
            'discount_amount': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
            'total_amount': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_class = 'form-horizontal'
        self.helper.label_class = 'col-lg-3'
        self.helper.field_class = 'col-lg-9'
        
        # Filter active clients and reservations
        self.fields['client'].queryset = Client.objects.filter(is_active=True)
        self.fields['reservation'].queryset = Reservation.objects.filter(
            status__in=['confirmed', 'pending']
        )
        self.fields['reservation'].required = False
        
        self.helper.layout = Layout(
            HTML('<h4 class="mb-3"><i class="fas fa-file-invoice"></i> معلومات الفاتورة</h4>'),
            Row(
                Column('client', css_class='form-group col-md-6'),
                Column('reservation', css_class='form-group col-md-6'),
            ),
            Row(
                Column('issue_date', css_class='form-group col-md-6'),
                Column('due_date', css_class='form-group col-md-6'),
            ),
            HTML('<hr>'),
            HTML('<h5 class="mb-3"><i class="fas fa-calculator"></i> التفاصيل المالية</h5>'),
            Row(
                Column('subtotal', css_class='form-group col-md-6'),
                Column('tax_rate', css_class='form-group col-md-6'),
            ),
            Row(
                Column('tax_amount', css_class='form-group col-md-6'),
                Column('discount_amount', css_class='form-group col-md-6'),
            ),
            'total_amount',
            HTML('<hr>'),
            'notes',
            'status',
            FormActions(
                Submit('submit', _('حفظ الفاتورة'), css_class='btn btn-primary'),
                HTML('<a href="{% url "finance:invoice_list" %}" class="btn btn-secondary">إلغاء</a>')
            )
        )


class InvoiceItemForm(forms.ModelForm):
    """Form for invoice items."""
    
    class Meta:
        model = InvoiceItem
        fields = ['description', 'quantity', 'unit_price', 'total_price']
        widgets = {
            'description': forms.TextInput(attrs={'class': 'form-control'}),
            'quantity': forms.NumberInput(attrs={'min': '1', 'class': 'form-control'}),
            'unit_price': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
            'total_price': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control', 'readonly': True}),
        }


class PaymentForm(forms.ModelForm):
    """Form for creating and editing payments."""
    
    class Meta:
        model = Payment
        fields = [
            'invoice', 'reservation', 'amount', 'payment_date',
            'payment_method', 'reference_number', 'notes', 'status'
        ]
        widgets = {
            'payment_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'amount': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
            'reference_number': forms.TextInput(attrs={'class': 'form-control'}),
            'notes': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_class = 'form-horizontal'
        self.helper.label_class = 'col-lg-3'
        self.helper.field_class = 'col-lg-9'
        
        # Filter unpaid invoices
        self.fields['invoice'].queryset = Invoice.objects.filter(
            status__in=['sent', 'draft']
        )
        self.fields['reservation'].queryset = Reservation.objects.filter(
            status='confirmed'
        )
        
        self.helper.layout = Layout(
            HTML('<h4 class="mb-3"><i class="fas fa-credit-card"></i> معلومات الدفع</h4>'),
            Row(
                Column('invoice', css_class='form-group col-md-6'),
                Column('reservation', css_class='form-group col-md-6'),
            ),
            Row(
                Column('amount', css_class='form-group col-md-6'),
                Column('payment_date', css_class='form-group col-md-6'),
            ),
            Row(
                Column('payment_method', css_class='form-group col-md-6'),
                Column('reference_number', css_class='form-group col-md-6'),
            ),
            'notes',
            'status',
            FormActions(
                Submit('submit', _('حفظ الدفع'), css_class='btn btn-primary'),
                HTML('<a href="{% url "finance:payment_list" %}" class="btn btn-secondary">إلغاء</a>')
            )
        )


class ExpenseForm(forms.ModelForm):
    """Form for creating and editing expenses."""
    
    class Meta:
        model = Expense
        fields = [
            'vendor', 'category', 'description', 'amount',
            'expense_date', 'receipt_number', 'notes', 'status'
        ]
        widgets = {
            'expense_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'amount': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
            'description': forms.TextInput(attrs={'class': 'form-control'}),
            'receipt_number': forms.TextInput(attrs={'class': 'form-control'}),
            'notes': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_class = 'form-horizontal'
        self.helper.label_class = 'col-lg-3'
        self.helper.field_class = 'col-lg-9'
        
        # Filter active suppliers
        self.fields['vendor'].queryset = Supplier.objects.filter(is_active=True)
        self.fields['vendor'].required = False
        
        self.helper.layout = Layout(
            HTML('<h4 class="mb-3"><i class="fas fa-receipt"></i> معلومات المصروف</h4>'),
            Row(
                Column('vendor', css_class='form-group col-md-6'),
                Column('category', css_class='form-group col-md-6'),
            ),
            'description',
            Row(
                Column('amount', css_class='form-group col-md-6'),
                Column('expense_date', css_class='form-group col-md-6'),
            ),
            Row(
                Column('receipt_number', css_class='form-group col-md-6'),
                Column('status', css_class='form-group col-md-6'),
            ),
            'notes',
            FormActions(
                Submit('submit', _('حفظ المصروف'), css_class='btn btn-primary'),
                HTML('<a href="{% url "finance:expense_list" %}" class="btn btn-secondary">إلغاء</a>')
            )
        )


class BankAccountForm(forms.ModelForm):
    """Form for creating and editing bank accounts."""
    
    class Meta:
        model = BankAccount
        fields = [
            'account_name', 'bank_name', 'account_number',
            'iban', 'swift_code', 'currency', 'balance',
            'is_active', 'notes'
        ]
        widgets = {
            'account_name': forms.TextInput(attrs={'class': 'form-control'}),
            'bank_name': forms.TextInput(attrs={'class': 'form-control'}),
            'account_number': forms.TextInput(attrs={'class': 'form-control'}),
            'iban': forms.TextInput(attrs={'class': 'form-control'}),
            'swift_code': forms.TextInput(attrs={'class': 'form-control'}),
            'balance': forms.NumberInput(attrs={'step': '0.01', 'class': 'form-control'}),
            'notes': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_class = 'form-horizontal'
        self.helper.label_class = 'col-lg-3'
        self.helper.field_class = 'col-lg-9'
        
        self.helper.layout = Layout(
            HTML('<h4 class="mb-3"><i class="fas fa-university"></i> معلومات الحساب المصرفي</h4>'),
            'account_name',
            Row(
                Column('bank_name', css_class='form-group col-md-6'),
                Column('account_number', css_class='form-group col-md-6'),
            ),
            Row(
                Column('iban', css_class='form-group col-md-6'),
                Column('swift_code', css_class='form-group col-md-6'),
            ),
            Row(
                Column('currency', css_class='form-group col-md-6'),
                Column('balance', css_class='form-group col-md-6'),
            ),
            'is_active',
            'notes',
            FormActions(
                Submit('submit', _('حفظ الحساب'), css_class='btn btn-primary'),
                HTML('<a href="{% url "finance:bank_account_list" %}" class="btn btn-secondary">إلغاء</a>')
            )
        )


class InvoiceFilterForm(forms.Form):
    """Form for filtering invoices."""
    
    status = forms.ChoiceField(
        choices=[('', 'جميع الحالات')] + Invoice.STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    client = forms.ModelChoiceField(
        queryset=Client.objects.filter(is_active=True),
        required=False,
        empty_label="جميع العملاء",
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'})
    )
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'})
    )
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'رقم الفاتورة أو اسم العميل...'
        })
    )


class PaymentFilterForm(forms.Form):
    """Form for filtering payments."""
    
    status = forms.ChoiceField(
        choices=[('', 'جميع الحالات')] + Payment.STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    payment_method = forms.ChoiceField(
        choices=[('', 'جميع الطرق')] + Payment.PAYMENT_METHOD_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'})
    )
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'})
    )


class ExpenseFilterForm(forms.Form):
    """Form for filtering expenses."""
    
    status = forms.ChoiceField(
        choices=[('', 'جميع الحالات')] + Expense.STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    category = forms.ChoiceField(
        choices=[('', 'جميع الفئات')] + Expense.CATEGORY_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    vendor = forms.ModelChoiceField(
        queryset=Supplier.objects.filter(is_active=True),
        required=False,
        empty_label="جميع الموردين",
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'})
    )
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'})
    )
