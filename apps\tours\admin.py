"""
Django admin configuration for Tours app.
"""
from django.contrib import admin
from django.utils.html import format_html
from django.utils.translation import gettext_lazy as _
from .models import Destination, TourCategory, TourPackage, Itinerary, PackageAvailability


@admin.register(Destination)
class DestinationAdmin(admin.ModelAdmin):
    """Admin interface for Destination model."""
    list_display = ['name_ar', 'name_fr', 'country', 'city', 'is_active', 'is_featured', 'popularity_score']
    list_filter = ['is_active', 'is_featured', 'country', 'created_at']
    search_fields = ['name_ar', 'name_fr', 'name_en', 'description_ar']
    list_editable = ['is_active', 'is_featured', 'popularity_score']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        (_('معلومات أساسية'), {
            'fields': ('name_ar', 'name_fr', 'name_en')
        }),
        (_('الوصف'), {
            'fields': ('description_ar', 'description_fr', 'description_en')
        }),
        (_('الموقع'), {
            'fields': ('country', 'city', 'latitude', 'longitude')
        }),
        (_('الوسائط'), {
            'fields': ('main_image', 'gallery')
        }),
        (_('الخصائص'), {
            'fields': ('climate', 'best_time_to_visit', 'activities')
        }),
        (_('الحالة'), {
            'fields': ('is_active', 'is_featured', 'popularity_score')
        }),
        (_('معلومات النظام'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(TourCategory)
class TourCategoryAdmin(admin.ModelAdmin):
    """Admin interface for TourCategory model."""
    list_display = ['name_ar', 'name_fr', 'is_active', 'sort_order', 'color_preview']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name_ar', 'name_fr', 'name_en', 'description']
    list_editable = ['is_active', 'sort_order']
    readonly_fields = ['created_at', 'updated_at']
    
    def color_preview(self, obj):
        """Display color preview."""
        if obj.color:
            return format_html(
                '<div style="width: 20px; height: 20px; background-color: {}; border: 1px solid #ccc;"></div>',
                obj.color
            )
        return '-'
    color_preview.short_description = _('معاينة اللون')
    
    fieldsets = (
        (_('معلومات أساسية'), {
            'fields': ('name_ar', 'name_fr', 'name_en', 'description')
        }),
        (_('التصميم'), {
            'fields': ('icon', 'color')
        }),
        (_('الإعدادات'), {
            'fields': ('is_active', 'sort_order')
        }),
        (_('معلومات النظام'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


class ItineraryInline(admin.TabularInline):
    """Inline admin for Itinerary model."""
    model = Itinerary
    extra = 1
    fields = ['day_number', 'title_ar', 'location', 'breakfast_included', 'lunch_included', 'dinner_included']


class PackageAvailabilityInline(admin.TabularInline):
    """Inline admin for PackageAvailability model."""
    model = PackageAvailability
    extra = 1
    fields = ['start_date', 'end_date', 'available_spots', 'is_available', 'discount_percentage']


@admin.register(TourPackage)
class TourPackageAdmin(admin.ModelAdmin):
    """Admin interface for TourPackage model."""
    list_display = ['package_code', 'title_ar', 'category', 'duration_days', 'base_price', 'is_active', 'is_featured']
    list_filter = ['is_active', 'is_featured', 'category', 'difficulty_level', 'created_at']
    search_fields = ['package_code', 'title_ar', 'title_fr', 'title_en', 'short_description_ar']
    list_editable = ['is_active', 'is_featured']
    readonly_fields = ['package_code', 'created_at', 'updated_at']
    filter_horizontal = ['destinations']
    inlines = [ItineraryInline, PackageAvailabilityInline]
    
    fieldsets = (
        (_('معلومات أساسية'), {
            'fields': ('package_code', 'title_ar', 'title_fr', 'title_en')
        }),
        (_('الوصف'), {
            'fields': ('short_description_ar', 'short_description_fr', 'short_description_en',
                      'detailed_description_ar', 'detailed_description_fr', 'detailed_description_en')
        }),
        (_('التصنيف'), {
            'fields': ('category', 'destinations')
        }),
        (_('المدة والتوقيت'), {
            'fields': ('duration_days', 'duration_nights', 'available_from', 'available_to')
        }),
        (_('الأسعار'), {
            'fields': ('base_price', 'child_price', 'infant_price')
        }),
        (_('السعة'), {
            'fields': ('min_participants', 'max_participants')
        }),
        (_('الخصائص'), {
            'fields': ('difficulty_level', 'physical_rating')
        }),
        (_('المشمول وغير المشمول'), {
            'fields': ('inclusions', 'exclusions', 'requirements')
        }),
        (_('القيود'), {
            'fields': ('age_restrictions', 'fitness_level')
        }),
        (_('الوسائط'), {
            'fields': ('main_image', 'gallery', 'video_url')
        }),
        (_('SEO والتسويق'), {
            'fields': ('meta_title', 'meta_description', 'keywords'),
            'classes': ('collapse',)
        }),
        (_('الحالة'), {
            'fields': ('is_active', 'is_featured', 'is_bestseller')
        }),
        (_('معلومات داخلية'), {
            'fields': ('created_by', 'notes'),
            'classes': ('collapse',)
        }),
        (_('معلومات النظام'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def save_model(self, request, obj, form, change):
        """Set created_by field when creating new package."""
        if not change:  # Creating new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(Itinerary)
class ItineraryAdmin(admin.ModelAdmin):
    """Admin interface for Itinerary model."""
    list_display = ['package', 'day_number', 'title_ar', 'location', 'meals_summary']
    list_filter = ['package', 'location', 'breakfast_included', 'lunch_included', 'dinner_included']
    search_fields = ['title_ar', 'title_fr', 'title_en', 'description_ar']
    readonly_fields = ['created_at', 'updated_at']
    
    def meals_summary(self, obj):
        """Display meals summary."""
        meals = []
        if obj.breakfast_included:
            meals.append('إفطار')
        if obj.lunch_included:
            meals.append('غداء')
        if obj.dinner_included:
            meals.append('عشاء')
        return ' + '.join(meals) if meals else 'لا توجد وجبات'
    meals_summary.short_description = _('الوجبات')
    
    fieldsets = (
        (_('معلومات أساسية'), {
            'fields': ('package', 'day_number')
        }),
        (_('العناوين'), {
            'fields': ('title_ar', 'title_fr', 'title_en')
        }),
        (_('الوصف'), {
            'fields': ('description_ar', 'description_fr', 'description_en')
        }),
        (_('الموقع والإقامة'), {
            'fields': ('location', 'accommodation')
        }),
        (_('الوجبات'), {
            'fields': ('breakfast_included', 'lunch_included', 'dinner_included')
        }),
        (_('الأنشطة'), {
            'fields': ('activities', 'optional_activities')
        }),
        (_('الوسائط'), {
            'fields': ('image',)
        }),
        (_('معلومات النظام'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(PackageAvailability)
class PackageAvailabilityAdmin(admin.ModelAdmin):
    """Admin interface for PackageAvailability model."""
    list_display = ['package', 'start_date', 'end_date', 'available_spots', 'reserved_spots', 'remaining_spots_display', 'is_available']
    list_filter = ['is_available', 'is_guaranteed', 'start_date', 'package']
    search_fields = ['package__title_ar', 'package__package_code']
    list_editable = ['is_available']
    readonly_fields = ['created_at', 'updated_at', 'remaining_spots_display']
    date_hierarchy = 'start_date'
    
    def remaining_spots_display(self, obj):
        """Display remaining spots."""
        remaining = obj.remaining_spots
        if remaining <= 0:
            return format_html('<span style="color: red;">ممتلئ</span>')
        elif remaining <= 5:
            return format_html('<span style="color: orange;">{}</span>', remaining)
        else:
            return format_html('<span style="color: green;">{}</span>', remaining)
    remaining_spots_display.short_description = _('الأماكن المتبقية')
    
    fieldsets = (
        (_('معلومات أساسية'), {
            'fields': ('package', 'start_date', 'end_date')
        }),
        (_('الأسعار'), {
            'fields': ('adult_price', 'child_price', 'infant_price')
        }),
        (_('التوفر'), {
            'fields': ('available_spots', 'reserved_spots', 'remaining_spots_display')
        }),
        (_('الحالة'), {
            'fields': ('is_available', 'is_guaranteed')
        }),
        (_('العروض الخاصة'), {
            'fields': ('discount_percentage', 'early_bird_discount')
        }),
        (_('ملاحظات'), {
            'fields': ('notes',)
        }),
        (_('معلومات النظام'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
