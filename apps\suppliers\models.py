"""
Supplier models for managing vendors and service providers.
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator, MaxValueValidator
from phonenumber_field.modelfields import PhoneNumberField
from djmoney.models.fields import MoneyField
from apps.core.models import AuditModel


class Supplier(AuditModel):
    """Main supplier model for vendors and service providers."""
    
    SUPPLIER_TYPE_CHOICES = [
        ('hotel', _('فندق')),
        ('restaurant', _('مطعم')),
        ('transport', _('نقل')),
        ('airline', _('خطوط جوية')),
        ('guide', _('مرشد سياحي')),
        ('activity', _('أنشطة')),
        ('insurance', _('تأمين')),
        ('visa', _('تأشيرات')),
        ('equipment', _('معدات')),
        ('other', _('أخرى')),
    ]
    
    RATING_CHOICES = [
        (1, _('ضعيف')),
        (2, _('مقبول')),
        (3, _('جيد')),
        (4, _('جيد جداً')),
        (5, _('ممتاز')),
    ]
    
    # Basic Information
    supplier_code = models.CharField(_('رمز المورد'), max_length=20, unique=True)
    name_ar = models.CharField(_('الاسم بالعربية'), max_length=200)
    name_fr = models.CharField(_('الاسم بالفرنسية'), max_length=200, blank=True)
    name_en = models.CharField(_('الاسم بالإنجليزية'), max_length=200, blank=True)
    
    # Classification
    supplier_type = models.CharField(_('نوع المورد'), max_length=20, choices=SUPPLIER_TYPE_CHOICES)
    category = models.CharField(_('الفئة'), max_length=100, blank=True)
    
    # Contact Information
    email = models.EmailField(_('البريد الإلكتروني'), blank=True)
    phone = PhoneNumberField(_('رقم الهاتف'))
    fax = PhoneNumberField(_('الفاكس'), blank=True)
    website = models.URLField(_('الموقع الإلكتروني'), blank=True)
    
    # Address
    address = models.TextField(_('العنوان'))
    city = models.ForeignKey('core.City', on_delete=models.SET_NULL, null=True, blank=True, verbose_name=_('المدينة'))
    postal_code = models.CharField(_('الرمز البريدي'), max_length=10, blank=True)
    
    # Business Information
    tax_number = models.CharField(_('الرقم الضريبي'), max_length=50, blank=True)
    commercial_register = models.CharField(_('السجل التجاري'), max_length=50, blank=True)
    license_number = models.CharField(_('رقم الترخيص'), max_length=50, blank=True)
    
    # Contact Person
    contact_person = models.CharField(_('الشخص المسؤول'), max_length=100)
    contact_title = models.CharField(_('المنصب'), max_length=100, blank=True)
    contact_phone = PhoneNumberField(_('هاتف الشخص المسؤول'), blank=True)
    contact_email = models.EmailField(_('بريد الشخص المسؤول'), blank=True)
    
    # Financial Information
    payment_terms = models.CharField(_('شروط الدفع'), max_length=100, blank=True)
    credit_limit = MoneyField(_('حد الائتمان'), max_digits=10, decimal_places=2, default_currency='MAD', null=True, blank=True)
    
    # Bank Details
    bank_name = models.CharField(_('اسم البنك'), max_length=100, blank=True)
    bank_account = models.CharField(_('رقم الحساب البنكي'), max_length=50, blank=True)
    iban = models.CharField(_('IBAN'), max_length=34, blank=True)
    
    # Rating and Performance
    rating = models.PositiveIntegerField(
        _('التقييم'),
        choices=RATING_CHOICES,
        null=True,
        blank=True,
        validators=[MinValueValidator(1), MaxValueValidator(5)]
    )
    performance_score = models.DecimalField(_('نقاط الأداء'), max_digits=5, decimal_places=2, null=True, blank=True)
    
    # Status
    is_active = models.BooleanField(_('نشط'), default=True)
    is_preferred = models.BooleanField(_('مورد مفضل'), default=False)
    is_blacklisted = models.BooleanField(_('في القائمة السوداء'), default=False)
    
    # Additional Information
    services_offered = models.TextField(_('الخدمات المقدمة'), blank=True)
    notes = models.TextField(_('ملاحظات'), blank=True)
    
    # Documents
    contract_file = models.FileField(_('ملف العقد'), upload_to='suppliers/contracts/', blank=True)
    license_file = models.FileField(_('ملف الترخيص'), upload_to='suppliers/licenses/', blank=True)
    
    class Meta:
        verbose_name = _('مورد')
        verbose_name_plural = _('الموردون')
        ordering = ['name_ar']
        indexes = [
            models.Index(fields=['supplier_code']),
            models.Index(fields=['supplier_type', 'is_active']),
            models.Index(fields=['rating']),
        ]
    
    def __str__(self):
        return f"{self.supplier_code} - {self.name_ar}"
    
    @property
    def display_name(self):
        """Return appropriate name based on language."""
        return self.name_ar or self.name_fr or self.name_en
    
    def save(self, *args, **kwargs):
        if not self.supplier_code:
            # Generate supplier code
            last_supplier = Supplier.objects.filter(supplier_code__startswith='SUP').order_by('-id').first()
            if last_supplier:
                last_number = int(last_supplier.supplier_code[3:])
                self.supplier_code = f"SUP{last_number + 1:06d}"
            else:
                self.supplier_code = "SUP000001"
        super().save(*args, **kwargs)


class SupplierContract(AuditModel):
    """Contract model for supplier agreements."""
    
    CONTRACT_TYPE_CHOICES = [
        ('service', _('خدمة')),
        ('product', _('منتج')),
        ('accommodation', _('إقامة')),
        ('transport', _('نقل')),
        ('activity', _('نشاط')),
        ('other', _('أخرى')),
    ]
    
    STATUS_CHOICES = [
        ('draft', _('مسودة')),
        ('active', _('نشط')),
        ('expired', _('منتهي الصلاحية')),
        ('terminated', _('منهي')),
        ('suspended', _('معلق')),
    ]
    
    # Basic Information
    contract_number = models.CharField(_('رقم العقد'), max_length=20, unique=True)
    supplier = models.ForeignKey(Supplier, on_delete=models.CASCADE, related_name='contracts', verbose_name=_('المورد'))
    
    # Contract Details
    title = models.CharField(_('عنوان العقد'), max_length=200)
    description = models.TextField(_('الوصف'), blank=True)
    contract_type = models.CharField(_('نوع العقد'), max_length=20, choices=CONTRACT_TYPE_CHOICES)
    
    # Dates
    start_date = models.DateField(_('تاريخ البداية'))
    end_date = models.DateField(_('تاريخ النهاية'))
    renewal_date = models.DateField(_('تاريخ التجديد'), null=True, blank=True)
    
    # Financial Terms
    contract_value = MoneyField(_('قيمة العقد'), max_digits=12, decimal_places=2, default_currency='MAD', null=True, blank=True)
    payment_terms = models.TextField(_('شروط الدفع'), blank=True)
    
    # Status
    status = models.CharField(_('الحالة'), max_length=20, choices=STATUS_CHOICES, default='draft')
    auto_renewal = models.BooleanField(_('تجديد تلقائي'), default=False)
    
    # Documents
    contract_file = models.FileField(_('ملف العقد'), upload_to='contracts/', blank=True)
    
    # Internal
    created_by = models.ForeignKey('accounts.User', on_delete=models.SET_NULL, null=True, verbose_name=_('أنشئ بواسطة'))
    approved_by = models.ForeignKey('accounts.User', on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_contracts', verbose_name=_('وافق عليه'))
    
    class Meta:
        verbose_name = _('عقد مورد')
        verbose_name_plural = _('عقود الموردين')
        ordering = ['-start_date']
        indexes = [
            models.Index(fields=['contract_number']),
            models.Index(fields=['supplier', 'status']),
            models.Index(fields=['end_date']),
        ]
    
    def __str__(self):
        return f"{self.contract_number} - {self.supplier.name_ar}"
    
    @property
    def is_active(self):
        """Check if contract is currently active."""
        from django.utils import timezone
        today = timezone.now().date()
        return self.status == 'active' and self.start_date <= today <= self.end_date
    
    @property
    def days_until_expiry(self):
        """Calculate days until contract expires."""
        from django.utils import timezone
        today = timezone.now().date()
        if self.end_date > today:
            return (self.end_date - today).days
        return 0
    
    def save(self, *args, **kwargs):
        if not self.contract_number:
            # Generate contract number
            last_contract = SupplierContract.objects.filter(contract_number__startswith='CON').order_by('-id').first()
            if last_contract:
                last_number = int(last_contract.contract_number[3:])
                self.contract_number = f"CON{last_number + 1:06d}"
            else:
                self.contract_number = "CON000001"
        super().save(*args, **kwargs)


class SupplierEvaluation(AuditModel):
    """Supplier performance evaluation model."""
    
    supplier = models.ForeignKey(Supplier, on_delete=models.CASCADE, related_name='evaluations', verbose_name=_('المورد'))
    
    # Evaluation Period
    evaluation_date = models.DateField(_('تاريخ التقييم'))
    period_start = models.DateField(_('بداية الفترة'))
    period_end = models.DateField(_('نهاية الفترة'))
    
    # Criteria Scores (1-5 scale)
    quality_score = models.PositiveIntegerField(
        _('نقاط الجودة'),
        validators=[MinValueValidator(1), MaxValueValidator(5)]
    )
    delivery_score = models.PositiveIntegerField(
        _('نقاط التسليم'),
        validators=[MinValueValidator(1), MaxValueValidator(5)]
    )
    service_score = models.PositiveIntegerField(
        _('نقاط الخدمة'),
        validators=[MinValueValidator(1), MaxValueValidator(5)]
    )
    price_score = models.PositiveIntegerField(
        _('نقاط السعر'),
        validators=[MinValueValidator(1), MaxValueValidator(5)]
    )
    communication_score = models.PositiveIntegerField(
        _('نقاط التواصل'),
        validators=[MinValueValidator(1), MaxValueValidator(5)]
    )
    
    # Overall Score
    overall_score = models.DecimalField(_('النقاط الإجمالية'), max_digits=3, decimal_places=1)
    
    # Comments
    strengths = models.TextField(_('نقاط القوة'), blank=True)
    weaknesses = models.TextField(_('نقاط الضعف'), blank=True)
    recommendations = models.TextField(_('التوصيات'), blank=True)
    
    # Evaluator
    evaluated_by = models.ForeignKey('accounts.User', on_delete=models.SET_NULL, null=True, verbose_name=_('قيم بواسطة'))
    
    class Meta:
        verbose_name = _('تقييم مورد')
        verbose_name_plural = _('تقييمات الموردين')
        ordering = ['-evaluation_date']
        unique_together = ['supplier', 'period_start', 'period_end']
    
    def __str__(self):
        return f"تقييم {self.supplier.name_ar} - {self.evaluation_date}"
    
    def save(self, *args, **kwargs):
        # Calculate overall score as average
        scores = [
            self.quality_score,
            self.delivery_score,
            self.service_score,
            self.price_score,
            self.communication_score
        ]
        self.overall_score = sum(scores) / len(scores)
        super().save(*args, **kwargs)
