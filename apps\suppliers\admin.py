"""
Django admin configuration for Suppliers app.
"""
from django.contrib import admin
from django.utils.html import format_html
from django.utils.translation import gettext_lazy as _
from .models import Supplier, SupplierContract, SupplierEvaluation


class SupplierContractInline(admin.TabularInline):
    """Inline admin for Supplier contracts."""
    model = SupplierContract
    extra = 0
    fields = ['contract_number', 'title', 'contract_type', 'start_date', 'end_date', 'status']
    readonly_fields = ['contract_number']


class SupplierEvaluationInline(admin.TabularInline):
    """Inline admin for Supplier evaluations."""
    model = SupplierEvaluation
    extra = 0
    fields = ['evaluation_date', 'overall_score', 'quality_score', 'service_score']
    readonly_fields = ['overall_score']


@admin.register(Supplier)
class SupplierAdmin(admin.ModelAdmin):
    """Admin interface for Supplier model."""
    list_display = ['supplier_code', 'name_ar', 'supplier_type', 'rating_display', 'performance_score', 'is_active', 'is_preferred']
    list_filter = ['supplier_type', 'rating', 'is_active', 'is_preferred', 'is_blacklisted', 'created_at']
    search_fields = ['supplier_code', 'name_ar', 'name_fr', 'name_en', 'email', 'contact_person']
    list_editable = ['is_active', 'is_preferred']
    readonly_fields = ['supplier_code', 'created_at', 'updated_at']
    inlines = [SupplierContractInline, SupplierEvaluationInline]
    
    def rating_display(self, obj):
        """Display rating with stars."""
        if obj.rating:
            stars = '⭐' * obj.rating
            return format_html(f'<span title="{obj.get_rating_display()}">{stars}</span>')
        return '-'
    rating_display.short_description = _('التقييم')
    
    fieldsets = (
        (_('معلومات أساسية'), {
            'fields': ('supplier_code', 'name_ar', 'name_fr', 'name_en')
        }),
        (_('التصنيف'), {
            'fields': ('supplier_type', 'category')
        }),
        (_('معلومات الاتصال'), {
            'fields': ('email', 'phone', 'fax', 'website')
        }),
        (_('العنوان'), {
            'fields': ('address', 'city', 'postal_code')
        }),
        (_('معلومات تجارية'), {
            'fields': ('tax_number', 'commercial_register', 'license_number'),
            'classes': ('collapse',)
        }),
        (_('الشخص المسؤول'), {
            'fields': ('contact_person', 'contact_title', 'contact_phone', 'contact_email')
        }),
        (_('المعلومات المالية'), {
            'fields': ('payment_terms', 'credit_limit'),
            'classes': ('collapse',)
        }),
        (_('تفاصيل البنك'), {
            'fields': ('bank_name', 'bank_account', 'iban'),
            'classes': ('collapse',)
        }),
        (_('التقييم والأداء'), {
            'fields': ('rating', 'performance_score')
        }),
        (_('الحالة'), {
            'fields': ('is_active', 'is_preferred', 'is_blacklisted')
        }),
        (_('معلومات إضافية'), {
            'fields': ('services_offered', 'notes'),
            'classes': ('collapse',)
        }),
        (_('الوثائق'), {
            'fields': ('contract_file', 'license_file'),
            'classes': ('collapse',)
        }),
        (_('معلومات النظام'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(SupplierContract)
class SupplierContractAdmin(admin.ModelAdmin):
    """Admin interface for SupplierContract model."""
    list_display = ['contract_number', 'supplier', 'title', 'contract_type', 'start_date', 'end_date', 'status', 'days_until_expiry_display']
    list_filter = ['contract_type', 'status', 'start_date', 'end_date', 'auto_renewal']
    search_fields = ['contract_number', 'title', 'supplier__name_ar', 'supplier__supplier_code']
    list_editable = ['status']
    readonly_fields = ['contract_number', 'is_active', 'days_until_expiry', 'created_at', 'updated_at']
    date_hierarchy = 'start_date'
    
    def days_until_expiry_display(self, obj):
        """Display days until expiry with color coding."""
        days = obj.days_until_expiry
        if days <= 0:
            return format_html('<span style="color: red;">منتهي</span>')
        elif days <= 30:
            return format_html('<span style="color: orange;">{} يوم</span>', days)
        else:
            return format_html('<span style="color: green;">{} يوم</span>', days)
    days_until_expiry_display.short_description = _('أيام حتى الانتهاء')
    
    fieldsets = (
        (_('معلومات أساسية'), {
            'fields': ('contract_number', 'supplier', 'title', 'description', 'contract_type')
        }),
        (_('التواريخ'), {
            'fields': ('start_date', 'end_date', 'renewal_date')
        }),
        (_('الشروط المالية'), {
            'fields': ('contract_value', 'payment_terms'),
            'classes': ('collapse',)
        }),
        (_('الحالة'), {
            'fields': ('status', 'auto_renewal', 'is_active', 'days_until_expiry')
        }),
        (_('الوثائق'), {
            'fields': ('contract_file',),
            'classes': ('collapse',)
        }),
        (_('معلومات النظام'), {
            'fields': ('created_by', 'approved_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def save_model(self, request, obj, form, change):
        """Set created_by field when creating new contract."""
        if not change:
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(SupplierEvaluation)
class SupplierEvaluationAdmin(admin.ModelAdmin):
    """Admin interface for SupplierEvaluation model."""
    list_display = ['supplier', 'evaluation_date', 'overall_score_display', 'quality_score', 'service_score', 'evaluated_by']
    list_filter = ['evaluation_date', 'overall_score', 'supplier__supplier_type']
    search_fields = ['supplier__name_ar', 'supplier__supplier_code']
    readonly_fields = ['overall_score', 'created_at', 'updated_at']
    date_hierarchy = 'evaluation_date'
    
    def overall_score_display(self, obj):
        """Display overall score with color coding."""
        score = obj.overall_score
        if score >= 4.0:
            color = 'green'
        elif score >= 3.0:
            color = 'orange'
        else:
            color = 'red'
        return format_html('<span style="color: {};">{}</span>', color, score)
    overall_score_display.short_description = _('النقاط الإجمالية')
    
    fieldsets = (
        (_('معلومات أساسية'), {
            'fields': ('supplier', 'evaluation_date')
        }),
        (_('فترة التقييم'), {
            'fields': ('period_start', 'period_end')
        }),
        (_('نقاط التقييم'), {
            'fields': (
                ('quality_score', 'delivery_score'),
                ('service_score', 'price_score'),
                ('communication_score', 'overall_score')
            )
        }),
        (_('التعليقات'), {
            'fields': ('strengths', 'weaknesses', 'recommendations'),
            'classes': ('collapse',)
        }),
        (_('معلومات النظام'), {
            'fields': ('evaluated_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def save_model(self, request, obj, form, change):
        """Set evaluated_by field when creating new evaluation."""
        if not change:
            obj.evaluated_by = request.user
        super().save_model(request, obj, form, change)
