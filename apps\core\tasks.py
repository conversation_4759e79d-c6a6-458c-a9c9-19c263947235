"""
Celery tasks for background processing.
"""
from celery import shared_task
from django.core.mail import send_mail
from django.conf import settings
from django.utils import timezone
from datetime import timedelta
import logging

logger = logging.getLogger(__name__)


@shared_task
def send_email_notification(subject, message, recipient_list, from_email=None):
    """
    Send email notification asynchronously.
    """
    try:
        if not from_email:
            from_email = settings.DEFAULT_FROM_EMAIL
        
        send_mail(
            subject=subject,
            message=message,
            from_email=from_email,
            recipient_list=recipient_list,
            fail_silently=False
        )
        logger.info(f"Email sent successfully to {recipient_list}")
        return True
    except Exception as e:
        logger.error(f"Failed to send email: {str(e)}")
        return False


@shared_task
def send_reservation_confirmation(reservation_id):
    """
    Send reservation confirmation email to client.
    """
    try:
        from apps.reservations.models import Reservation
        
        reservation = Reservation.objects.get(id=reservation_id)
        
        subject = f"تأكيد الحجز - {reservation.reservation_number}"
        message = f"""
        عزيزي/عزيزتي {reservation.client.full_name_ar},
        
        تم تأكيد حجزكم بنجاح.
        
        تفاصيل الحجز:
        - رقم الحجز: {reservation.reservation_number}
        - الباقة: {reservation.package.title_ar}
        - تاريخ المغادرة: {reservation.departure_date}
        - تاريخ العودة: {reservation.return_date}
        - عدد المشاركين: {reservation.total_participants}
        - المبلغ الإجمالي: {reservation.total_amount}
        
        شكراً لاختياركم خدماتنا.
        
        مع أطيب التحيات،
        فريق وكالة السفر المغربية
        """
        
        send_email_notification.delay(
            subject=subject,
            message=message,
            recipient_list=[reservation.client.email]
        )
        
        # Update confirmation status
        reservation.confirmation_sent = True
        reservation.confirmation_sent_at = timezone.now()
        reservation.save()
        
        logger.info(f"Reservation confirmation sent for {reservation.reservation_number}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to send reservation confirmation: {str(e)}")
        return False


@shared_task
def send_invoice_reminder(invoice_id):
    """
    Send invoice payment reminder to client.
    """
    try:
        from apps.finance.models import Invoice
        
        invoice = Invoice.objects.get(id=invoice_id)
        
        if invoice.status == 'paid':
            return True  # Already paid, no need to send reminder
        
        subject = f"تذكير بدفع الفاتورة - {invoice.invoice_number}"
        message = f"""
        عزيزي/عزيزتي {invoice.client.full_name_ar},
        
        نذكركم بضرورة دفع الفاتورة التالية:
        
        - رقم الفاتورة: {invoice.invoice_number}
        - تاريخ الاستحقاق: {invoice.due_date}
        - المبلغ المستحق: {invoice.remaining_amount} درهم
        
        يرجى الدفع في أقرب وقت ممكن لتجنب أي تأخير في خدماتكم.
        
        مع أطيب التحيات،
        قسم المحاسبة
        """
        
        send_email_notification.delay(
            subject=subject,
            message=message,
            recipient_list=[invoice.client.email]
        )
        
        logger.info(f"Invoice reminder sent for {invoice.invoice_number}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to send invoice reminder: {str(e)}")
        return False


@shared_task
def check_overdue_invoices():
    """
    Check for overdue invoices and send reminders.
    """
    try:
        from apps.finance.models import Invoice
        
        # Get overdue invoices
        today = timezone.now().date()
        overdue_invoices = Invoice.objects.filter(
            due_date__lt=today,
            status__in=['sent', 'overdue']
        )
        
        for invoice in overdue_invoices:
            # Update status to overdue
            if invoice.status != 'overdue':
                invoice.status = 'overdue'
                invoice.save()
            
            # Send reminder (limit to once per week)
            last_reminder = getattr(invoice, 'last_reminder_sent', None)
            if not last_reminder or (today - last_reminder).days >= 7:
                send_invoice_reminder.delay(invoice.id)
                invoice.last_reminder_sent = today
                invoice.save()
        
        logger.info(f"Checked {overdue_invoices.count()} overdue invoices")
        return overdue_invoices.count()
        
    except Exception as e:
        logger.error(f"Failed to check overdue invoices: {str(e)}")
        return 0


@shared_task
def backup_database():
    """
    Create database backup.
    """
    try:
        import subprocess
        from django.conf import settings
        
        # Get database settings
        db_settings = settings.DATABASES['default']
        
        # Create backup filename with timestamp
        timestamp = timezone.now().strftime('%Y%m%d_%H%M%S')
        backup_file = f"backup_{timestamp}.sql"
        backup_path = f"/tmp/{backup_file}"
        
        # Create backup command
        cmd = [
            'pg_dump',
            f"--host={db_settings['HOST']}",
            f"--port={db_settings['PORT']}",
            f"--username={db_settings['USER']}",
            f"--dbname={db_settings['NAME']}",
            f"--file={backup_path}",
            '--verbose'
        ]
        
        # Set password environment variable
        env = {'PGPASSWORD': db_settings['PASSWORD']}
        
        # Execute backup
        result = subprocess.run(cmd, env=env, capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info(f"Database backup created successfully: {backup_path}")
            return backup_path
        else:
            logger.error(f"Database backup failed: {result.stderr}")
            return None
            
    except Exception as e:
        logger.error(f"Failed to create database backup: {str(e)}")
        return None


@shared_task
def generate_daily_reports():
    """
    Generate daily reports and send to management.
    """
    try:
        from apps.reservations.models import Reservation
        from apps.finance.models import Invoice, Payment
        from apps.crm.models import Client
        
        today = timezone.now().date()
        yesterday = today - timedelta(days=1)
        
        # Get statistics
        new_reservations = Reservation.objects.filter(
            booking_date__date=yesterday
        ).count()
        
        new_clients = Client.objects.filter(
            created_at__date=yesterday
        ).count()
        
        payments_received = Payment.objects.filter(
            payment_date__date=yesterday,
            status='completed'
        ).aggregate(
            total=models.Sum('amount')
        )['total'] or 0
        
        # Generate report
        report = f"""
        التقرير اليومي - {yesterday.strftime('%Y-%m-%d')}
        
        الإحصائيات:
        - حجوزات جديدة: {new_reservations}
        - عملاء جدد: {new_clients}
        - المدفوعات المستلمة: {payments_received} درهم
        
        تم إنشاء هذا التقرير تلقائياً في {timezone.now().strftime('%Y-%m-%d %H:%M:%S')}
        """
        
        # Send to management
        management_emails = ['<EMAIL>']  # Configure as needed
        
        send_email_notification.delay(
            subject=f"التقرير اليومي - {yesterday.strftime('%Y-%m-%d')}",
            message=report,
            recipient_list=management_emails
        )
        
        logger.info(f"Daily report generated for {yesterday}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to generate daily report: {str(e)}")
        return False


@shared_task
def sync_offline_data():
    """
    Sync offline data when connection is restored.
    """
    try:
        # This would implement offline data synchronization
        # For now, it's a placeholder for future implementation
        
        logger.info("Offline data sync completed")
        return True
        
    except Exception as e:
        logger.error(f"Failed to sync offline data: {str(e)}")
        return False


@shared_task
def cleanup_old_files():
    """
    Clean up old temporary files and logs.
    """
    try:
        import os
        import glob
        
        # Clean up old backup files (keep last 30 days)
        backup_pattern = "/tmp/backup_*.sql"
        cutoff_date = timezone.now() - timedelta(days=30)
        
        for backup_file in glob.glob(backup_pattern):
            file_time = timezone.datetime.fromtimestamp(os.path.getmtime(backup_file))
            if file_time < cutoff_date:
                os.remove(backup_file)
                logger.info(f"Removed old backup file: {backup_file}")
        
        # Clean up old log files
        log_pattern = "/var/log/django/*.log.*"
        for log_file in glob.glob(log_pattern):
            file_time = timezone.datetime.fromtimestamp(os.path.getmtime(log_file))
            if file_time < cutoff_date:
                os.remove(log_file)
                logger.info(f"Removed old log file: {log_file}")
        
        logger.info("File cleanup completed")
        return True
        
    except Exception as e:
        logger.error(f"Failed to cleanup old files: {str(e)}")
        return False


@shared_task
def update_exchange_rates():
    """
    Update currency exchange rates from external API.
    """
    try:
        import requests
        from apps.core.models import Currency
        
        # This is a placeholder - you would integrate with a real exchange rate API
        # For example: https://api.exchangerate-api.com/v4/latest/MAD
        
        base_currency = 'MAD'
        api_url = f"https://api.exchangerate-api.com/v4/latest/{base_currency}"
        
        response = requests.get(api_url, timeout=10)
        if response.status_code == 200:
            data = response.json()
            rates = data.get('rates', {})
            
            # Update exchange rates
            for currency_code, rate in rates.items():
                try:
                    currency = Currency.objects.get(code=currency_code)
                    currency.exchange_rate = 1 / rate  # Convert to MAD base
                    currency.save()
                    logger.info(f"Updated exchange rate for {currency_code}: {currency.exchange_rate}")
                except Currency.DoesNotExist:
                    continue
            
            logger.info("Exchange rates updated successfully")
            return True
        else:
            logger.error(f"Failed to fetch exchange rates: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        logger.error(f"Failed to update exchange rates: {str(e)}")
        return False
