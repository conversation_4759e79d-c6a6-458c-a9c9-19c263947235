"""
URL configuration for reservations app.
"""
from django.urls import path
from . import views

app_name = 'reservations'

urlpatterns = [
    # Reservation URLs
    path('', views.ReservationListView.as_view(), name='reservation_list'),
    path('reservations/', views.ReservationListView.as_view(), name='reservation_list'),
    path('reservations/add/', views.ReservationCreateView.as_view(), name='reservation_add'),
    path('reservations/<int:pk>/', views.ReservationDetailView.as_view(), name='reservation_detail'),
    path('reservations/<int:pk>/edit/', views.ReservationUpdateView.as_view(), name='reservation_edit'),
    path('reservations/<int:pk>/cancel/', views.ReservationCancelView.as_view(), name='reservation_cancel'),
    path('reservations/<int:pk>/confirm/', views.ReservationConfirmView.as_view(), name='reservation_confirm'),
    
    # Participant URLs
    path('reservations/<int:reservation_id>/participants/', views.ParticipantListView.as_view(), name='participant_list'),
    path('reservations/<int:reservation_id>/participants/add/', views.ParticipantCreateView.as_view(), name='participant_add'),
    path('participants/<int:pk>/edit/', views.ParticipantUpdateView.as_view(), name='participant_edit'),
    
    # Service URLs
    path('reservations/<int:reservation_id>/services/', views.ServiceListView.as_view(), name='service_list'),
    path('reservations/<int:reservation_id>/services/add/', views.ServiceCreateView.as_view(), name='service_add'),
    path('services/<int:pk>/edit/', views.ServiceUpdateView.as_view(), name='service_edit'),
    
    # Document URLs
    path('reservations/<int:reservation_id>/documents/', views.DocumentListView.as_view(), name='document_list'),
    path('reservations/<int:reservation_id>/documents/add/', views.DocumentCreateView.as_view(), name='document_add'),
    
    # AJAX URLs
    path('ajax/package-info/<int:pk>/', views.PackageInfoAjaxView.as_view(), name='package_info_ajax'),
    path('ajax/availability/<int:package_id>/', views.AvailabilityAjaxView.as_view(), name='availability_ajax'),
]
