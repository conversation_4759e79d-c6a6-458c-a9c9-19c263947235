"""
Reports views for the Moroccan Travel Agency ERP system.
"""
from django.shortcuts import render
from django.views.generic import TemplateView
from django.db.models import Count, Sum, Avg, Q
from django.utils import timezone
from datetime import datetime, timedelta
from apps.crm.models import Client
from apps.tours.models import TourPackage, Destination, TourCategory
from apps.accounts.models import User
from apps.core.models import Country, City


class ReportsView(TemplateView):
    """Main reports dashboard."""
    template_name = 'reports/dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Date ranges
        today = timezone.now().date()
        last_30_days = today - timedelta(days=30)
        last_7_days = today - timedelta(days=7)

        # Client statistics
        context.update({
            'total_clients': Client.objects.count(),
            'new_clients_30_days': Client.objects.filter(created_at__gte=last_30_days).count(),
            'new_clients_7_days': Client.objects.filter(created_at__gte=last_7_days).count(),
            'vip_clients': Client.objects.filter(vip_status=True).count(),
            'individual_clients': Client.objects.filter(client_type='individual').count(),
            'corporate_clients': Client.objects.filter(client_type='corporate').count(),
        })

        # Package statistics
        context.update({
            'total_packages': TourPackage.objects.count(),
            'active_packages': TourPackage.objects.filter(is_active=True).count(),
            'featured_packages': TourPackage.objects.filter(is_featured=True).count(),
            'total_destinations': Destination.objects.count(),
            'total_categories': TourCategory.objects.count(),
        })

        # Geographic statistics
        context.update({
            'total_countries': Country.objects.count(),
            'total_cities': City.objects.count(),
        })

        # User statistics
        context.update({
            'total_users': User.objects.count(),
            'active_users': User.objects.filter(is_active=True).count(),
            'staff_users': User.objects.filter(is_staff=True).count(),
        })

        # Client distribution by nationality (safe query)
        try:
            client_by_nationality = Client.objects.select_related('nationality').values('nationality__name_ar').annotate(
                count=Count('id')
            ).order_by('-count')[:10]
        except:
            client_by_nationality = []
        context['client_by_nationality'] = client_by_nationality

        # Package distribution by category (safe query)
        try:
            package_by_category = TourPackage.objects.select_related('category').values('category__name_ar').annotate(
                count=Count('id')
            ).order_by('-count')
        except:
            package_by_category = []
        context['package_by_category'] = package_by_category

        # Recent activity
        context.update({
            'recent_clients': Client.objects.order_by('-created_at')[:10],
            'recent_packages': TourPackage.objects.order_by('-created_at')[:5],
        })

        return context


class ClientReportsView(TemplateView):
    """Detailed client reports."""
    template_name = 'reports/clients.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Client analysis
        clients = Client.objects.all()

        # Age distribution (if birth dates available)
        today = timezone.now().date()
        age_groups = {
            'age_18_25': clients.filter(date_of_birth__lte=today - timedelta(days=18*365),
                                      date_of_birth__gte=today - timedelta(days=25*365)).count(),
            'age_26_35': clients.filter(date_of_birth__lte=today - timedelta(days=26*365),
                                      date_of_birth__gte=today - timedelta(days=35*365)).count(),
            'age_36_50': clients.filter(date_of_birth__lte=today - timedelta(days=36*365),
                                      date_of_birth__gte=today - timedelta(days=50*365)).count(),
            'age_50_plus': clients.filter(date_of_birth__lt=today - timedelta(days=50*365)).count(),
        }

        # Gender distribution
        gender_distribution = {
            'male': clients.filter(gender='male').count(),
            'female': clients.filter(gender='female').count(),
            'other': clients.filter(gender='other').count(),
        }

        # Language preferences (safe query)
        try:
            language_distribution = clients.values('preferred_language').annotate(
                count=Count('id')
            ).order_by('-count')
        except:
            language_distribution = []

        # VIP status distribution
        vip_distribution = {
            'vip': clients.filter(vip_status=True).count(),
            'regular': clients.filter(vip_status=False).count(),
        }

        # Loyalty points analysis (safe query)
        try:
            loyalty_stats = clients.aggregate(
                avg_points=Avg('loyalty_points'),
                total_points=Sum('loyalty_points')
            )
            max_client = clients.order_by('-loyalty_points').first()
            loyalty_stats['max_points'] = max_client.loyalty_points if max_client else 0
        except:
            loyalty_stats = {'avg_points': 0, 'total_points': 0, 'max_points': 0}

        context.update({
            'total_clients': clients.count(),
            'age_groups': age_groups,
            'gender_distribution': gender_distribution,
            'language_distribution': language_distribution,
            'vip_distribution': vip_distribution,
            'loyalty_stats': loyalty_stats,
            'top_clients_by_points': clients.order_by('-loyalty_points')[:10],
        })

        return context


class PackageReportsView(TemplateView):
    """Detailed package reports."""
    template_name = 'reports/packages.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        packages = TourPackage.objects.all()

        # Package statistics
        context.update({
            'total_packages': packages.count(),
            'active_packages': packages.filter(is_active=True).count(),
            'featured_packages': packages.filter(is_featured=True).count(),
            'inactive_packages': packages.filter(is_active=False).count(),
        })

        # Price analysis (safe query)
        try:
            price_stats = packages.aggregate(avg_price=Avg('base_price'))
            min_package = packages.order_by('base_price').first()
            max_package = packages.order_by('-base_price').first()
            price_stats['min_price'] = min_package.base_price if min_package else 0
            price_stats['max_price'] = max_package.base_price if max_package else 0
        except:
            price_stats = {'avg_price': 0, 'min_price': 0, 'max_price': 0}

        # Duration analysis (safe query)
        try:
            duration_stats = packages.aggregate(avg_duration=Avg('duration_days'))
            min_duration_package = packages.order_by('duration_days').first()
            max_duration_package = packages.order_by('-duration_days').first()
            duration_stats['min_duration'] = min_duration_package.duration_days if min_duration_package else 0
            duration_stats['max_duration'] = max_duration_package.duration_days if max_duration_package else 0
        except:
            duration_stats = {'avg_duration': 0, 'min_duration': 0, 'max_duration': 0}

        # Category distribution (safe query)
        try:
            category_distribution = packages.select_related('category').values('category__name_ar').annotate(
                count=Count('id')
            ).order_by('-count')
        except:
            category_distribution = []

        # Difficulty level distribution (safe query)
        try:
            difficulty_distribution = packages.values('difficulty_level').annotate(
                count=Count('id')
            ).order_by('-count')
        except:
            difficulty_distribution = []

        context.update({
            'price_stats': price_stats,
            'duration_stats': duration_stats,
            'category_distribution': category_distribution,
            'difficulty_distribution': difficulty_distribution,
            'most_expensive_packages': packages.order_by('-base_price')[:5],
            'longest_packages': packages.order_by('-duration_days')[:5],
        })

        return context
