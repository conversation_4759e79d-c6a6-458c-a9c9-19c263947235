<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة {{ invoice.invoice_number }}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        
        .email-container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            margin: 0;
            font-size: 24px;
        }
        
        .header p {
            margin: 5px 0 0 0;
            opacity: 0.9;
        }
        
        .invoice-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 25px;
            border-right: 4px solid #667eea;
        }
        
        .invoice-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 25px;
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .detail-label {
            font-weight: 600;
            color: #6c757d;
        }
        
        .detail-value {
            color: #495057;
        }
        
        .amount-highlight {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin: 25px 0;
        }
        
        .amount-highlight h2 {
            color: #667eea;
            margin: 0;
            font-size: 28px;
        }
        
        .status-badge {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
        }
        
        .status-draft {
            background: #f8f9fa;
            color: #6c757d;
        }
        
        .status-sent {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-paid {
            background: #d1edff;
            color: #0c63e4;
        }
        
        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 500;
            margin: 20px 0;
            transition: all 0.3s ease;
        }
        
        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            color: white;
            text-decoration: none;
        }
        
        .footer {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin-top: 30px;
            color: #6c757d;
            font-size: 14px;
        }
        
        .footer a {
            color: #667eea;
            text-decoration: none;
        }
        
        .company-info {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #e9ecef;
        }
        
        @media (max-width: 600px) {
            .invoice-details {
                grid-template-columns: 1fr;
            }
            
            .email-container {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="header">
            <h1>🧾 فاتورة جديدة</h1>
            <p>وكالة السفر المغربية</p>
        </div>
        
        <!-- Greeting -->
        <div style="margin-bottom: 25px;">
            <h2 style="color: #495057; margin-bottom: 10px;">مرحباً {{ client_name }}،</h2>
            <p style="color: #6c757d; font-size: 16px; line-height: 1.6;">
                نتشرف بإرسال فاتورتكم الجديدة. يرجى مراجعة التفاصيل أدناه والتأكد من صحة جميع المعلومات.
            </p>
        </div>
        
        <!-- Invoice Info -->
        <div class="invoice-info">
            <h3 style="margin-top: 0; color: #667eea;">تفاصيل الفاتورة</h3>
            <div class="invoice-details">
                <div>
                    <div class="detail-item">
                        <span class="detail-label">رقم الفاتورة:</span>
                        <span class="detail-value">{{ invoice.invoice_number }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">تاريخ الإصدار:</span>
                        <span class="detail-value">{{ invoice.issue_date }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">تاريخ الاستحقاق:</span>
                        <span class="detail-value">{{ invoice.due_date }}</span>
                    </div>
                </div>
                <div>
                    <div class="detail-item">
                        <span class="detail-label">الحالة:</span>
                        <span class="status-badge status-{{ invoice.status }}">{{ invoice.get_status_display }}</span>
                    </div>
                    {% if invoice.reservation %}
                    <div class="detail-item">
                        <span class="detail-label">رقم الحجز:</span>
                        <span class="detail-value">{{ invoice.reservation.reservation_number }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">الباقة:</span>
                        <span class="detail-value">{{ invoice.reservation.package.title_ar }}</span>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Amount Highlight -->
        <div class="amount-highlight">
            <p style="margin: 0; color: #6c757d; font-size: 16px;">المبلغ الإجمالي</p>
            <h2>{{ invoice.total_amount }} درهم</h2>
            <p style="margin: 0; color: #6c757d; font-size: 14px;">
                شامل الضريبة ({{ invoice.tax_rate }}%)
            </p>
        </div>
        
        <!-- Payment Instructions -->
        {% if invoice.status != 'paid' %}
        <div style="background: #fff3cd; padding: 20px; border-radius: 10px; margin: 25px 0; border-right: 4px solid #ffc107;">
            <h4 style="margin-top: 0; color: #856404;">تعليمات الدفع</h4>
            <p style="color: #856404; margin-bottom: 15px;">
                يرجى سداد المبلغ المستحق قبل تاريخ الاستحقاق لتجنب أي رسوم إضافية.
            </p>
            <div style="color: #856404;">
                <strong>طرق الدفع المتاحة:</strong>
                <ul style="margin: 10px 0;">
                    <li>تحويل بنكي</li>
                    <li>دفع نقدي في المكتب</li>
                    <li>بطاقة ائتمانية</li>
                    <li>شيك مصرفي</li>
                </ul>
            </div>
        </div>
        {% endif %}
        
        <!-- Call to Action -->
        <div style="text-align: center; margin: 30px 0;">
            <a href="#" class="cta-button">
                📄 عرض الفاتورة كاملة
            </a>
        </div>
        
        <!-- Additional Notes -->
        {% if invoice.notes %}
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 25px 0;">
            <h4 style="margin-top: 0; color: #495057;">ملاحظات إضافية</h4>
            <p style="color: #6c757d; margin: 0;">{{ invoice.notes }}</p>
        </div>
        {% endif %}
        
        <!-- Contact Information -->
        <div style="background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%); padding: 20px; border-radius: 10px; margin: 25px 0;">
            <h4 style="margin-top: 0; color: #667eea;">هل تحتاج مساعدة؟</h4>
            <p style="color: #6c757d; margin-bottom: 15px;">
                فريق خدمة العملاء متاح لمساعدتكم في أي استفسارات حول الفاتورة أو الحجز.
            </p>
            <div style="color: #6c757d;">
                <p style="margin: 5px 0;">📞 الهاتف: +212 5 22 XX XX XX</p>
                <p style="margin: 5px 0;">📧 البريد: <EMAIL></p>
                <p style="margin: 5px 0;">🕒 ساعات العمل: 9:00 - 18:00 (الاثنين - الجمعة)</p>
            </div>
        </div>
        
        <!-- Footer -->
        <div class="footer">
            <p style="margin: 0; font-weight: 600;">شكراً لثقتكم في وكالة السفر المغربية</p>
            <p style="margin: 5px 0 0 0;">نتطلع لخدمتكم في رحلات قادمة مميزة</p>
            
            <div class="company-info">
                <p style="margin: 0;"><strong>وكالة السفر المغربية</strong></p>
                <p style="margin: 5px 0;">العنوان: الدار البيضاء، المغرب</p>
                <p style="margin: 5px 0;">
                    الموقع: <a href="https://moroccantravel.ma">www.moroccantravel.ma</a>
                </p>
                <p style="margin: 5px 0; font-size: 12px; color: #adb5bd;">
                    هذا البريد الإلكتروني تم إرساله تلقائياً، يرجى عدم الرد عليه مباشرة.
                </p>
            </div>
        </div>
    </div>
</body>
</html>
