{% extends 'base.html' %}
{% load i18n %}
{% load crispy_forms_tags %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    .form-container {
        background: white;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }
    .form-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px;
        text-align: center;
    }
    .form-body {
        padding: 30px;
    }
    .nav-tabs .nav-link {
        border-radius: 20px 20px 0 0;
        border: none;
        color: #667eea;
        font-weight: 500;
        margin-right: 5px;
    }
    .nav-tabs .nav-link.active {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
    .tab-content {
        background: #f8f9fa;
        border-radius: 0 0 15px 15px;
        padding: 30px;
        min-height: 400px;
    }
    .form-control, .form-select {
        border-radius: 10px;
        border: 2px solid #e9ecef;
        padding: 12px 15px;
        transition: all 0.3s ease;
    }
    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 25px;
        padding: 12px 30px;
        font-weight: 500;
        transition: transform 0.3s ease;
    }
    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    }
    .btn-secondary {
        border-radius: 25px;
        padding: 12px 30px;
        font-weight: 500;
    }
    .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 8px;
    }
    .invalid-feedback {
        display: block;
        font-size: 0.875rem;
        color: #dc3545;
        margin-top: 5px;
    }
    .progress-indicator {
        height: 4px;
        background: #e9ecef;
        border-radius: 2px;
        overflow: hidden;
        margin-bottom: 20px;
    }
    .progress-bar {
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        transition: width 0.3s ease;
    }
    .step-indicator {
        display: flex;
        justify-content: center;
        margin-bottom: 30px;
    }
    .step {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: #e9ecef;
        color: #6c757d;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 10px;
        font-weight: bold;
        transition: all 0.3s ease;
    }
    .step.active {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
    .step.completed {
        background: #28a745;
        color: white;
    }
    .floating-label {
        position: relative;
    }
    .floating-label .form-control {
        padding-top: 20px;
    }
    .floating-label label {
        position: absolute;
        top: 15px;
        left: 15px;
        transition: all 0.3s ease;
        pointer-events: none;
        color: #6c757d;
    }
    .floating-label .form-control:focus + label,
    .floating-label .form-control:not(:placeholder-shown) + label {
        top: 5px;
        font-size: 0.75rem;
        color: #667eea;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item">
                <a href="{% url 'crm:dashboard' %}">
                    <i class="fas fa-home"></i>
                    {% trans "الرئيسية" %}
                </a>
            </li>
            <li class="breadcrumb-item">
                <a href="{% url 'crm:client_list' %}">{% trans "العملاء" %}</a>
            </li>
            <li class="breadcrumb-item active">
                {% if form_action == 'create' %}
                    {% trans "إضافة عميل جديد" %}
                {% else %}
                    {% trans "تعديل العميل" %}
                {% endif %}
            </li>
        </ol>
    </nav>

    <!-- Form Container -->
    <div class="row justify-content-center">
        <div class="col-xl-10 col-lg-12">
            <div class="form-container">
                <!-- Form Header -->
                <div class="form-header">
                    <h2 class="mb-0">
                        {% if form_action == 'create' %}
                            <i class="fas fa-user-plus me-3"></i>
                            {% trans "إضافة عميل جديد" %}
                        {% else %}
                            <i class="fas fa-user-edit me-3"></i>
                            {% trans "تعديل بيانات العميل" %}
                        {% endif %}
                    </h2>
                    <p class="mb-0 mt-2 opacity-75">
                        {% trans "يرجى ملء جميع الحقول المطلوبة بعناية" %}
                    </p>
                </div>

                <!-- Form Body -->
                <div class="form-body">
                    <!-- Progress Indicator -->
                    <div class="progress-indicator">
                        <div class="progress-bar" style="width: 20%"></div>
                    </div>

                    <!-- Step Indicator -->
                    <div class="step-indicator">
                        <div class="step active" data-step="1">1</div>
                        <div class="step" data-step="2">2</div>
                        <div class="step" data-step="3">3</div>
                        <div class="step" data-step="4">4</div>
                        <div class="step" data-step="5">5</div>
                    </div>

                    <!-- Form -->
                    <form method="post" id="client-form" class="needs-validation" novalidate>
                        {% csrf_token %}
                        
                        <!-- Success/Error Messages -->
                        {% if messages %}
                        <div class="alert-container mb-4">
                            {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                <i class="fas fa-check-circle me-2"></i>
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                            {% endfor %}
                        </div>
                        {% endif %}

                        <!-- Form Errors -->
                        {% if form.non_field_errors %}
                        <div class="alert alert-danger" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            {% for error in form.non_field_errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}

                        <!-- Crispy Form -->
                        {% crispy form %}
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center py-4">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">{% trans "جاري التحميل..." %}</span>
                </div>
                <h5>{% trans "جاري حفظ البيانات..." %}</h5>
                <p class="text-muted mb-0">{% trans "يرجى الانتظار" %}</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('client-form');
    const steps = document.querySelectorAll('.step');
    const progressBar = document.querySelector('.progress-bar');
    const tabs = document.querySelectorAll('.nav-link');
    
    // Update progress based on active tab
    function updateProgress() {
        const activeTab = document.querySelector('.nav-link.active');
        if (activeTab) {
            const tabIndex = Array.from(tabs).indexOf(activeTab);
            const progress = ((tabIndex + 1) / tabs.length) * 100;
            progressBar.style.width = progress + '%';
            
            // Update step indicators
            steps.forEach((step, index) => {
                step.classList.remove('active', 'completed');
                if (index < tabIndex) {
                    step.classList.add('completed');
                } else if (index === tabIndex) {
                    step.classList.add('active');
                }
            });
        }
    }
    
    // Listen for tab changes
    tabs.forEach(tab => {
        tab.addEventListener('shown.bs.tab', updateProgress);
    });
    
    // Form validation
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        if (!form.checkValidity()) {
            e.stopPropagation();
            form.classList.add('was-validated');
            
            // Find first invalid field and focus on it
            const firstInvalid = form.querySelector(':invalid');
            if (firstInvalid) {
                firstInvalid.focus();
                
                // Switch to tab containing the invalid field
                const tabPane = firstInvalid.closest('.tab-pane');
                if (tabPane) {
                    const tabId = tabPane.id;
                    const tabLink = document.querySelector(`[href="#${tabId}"]`);
                    if (tabLink) {
                        new bootstrap.Tab(tabLink).show();
                    }
                }
            }
            return;
        }
        
        // Show loading modal
        const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
        loadingModal.show();
        
        // Submit form via AJAX
        const formData = new FormData(form);
        
        fetch(form.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            loadingModal.hide();
            
            if (data.success) {
                // Show success message
                const alertHtml = `
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        ${data.message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                `;
                
                const alertContainer = document.querySelector('.alert-container') || 
                                     document.querySelector('.form-body');
                alertContainer.insertAdjacentHTML('afterbegin', alertHtml);
                
                // Redirect after delay
                setTimeout(() => {
                    window.location.href = data.redirect_url;
                }, 1500);
            } else {
                // Show validation errors
                displayFormErrors(data.errors);
            }
        })
        .catch(error => {
            loadingModal.hide();
            console.error('Error:', error);
            alert('{% trans "حدث خطأ أثناء حفظ البيانات" %}');
        });
    });
    
    // Display form errors
    function displayFormErrors(errors) {
        // Clear previous errors
        document.querySelectorAll('.invalid-feedback').forEach(el => el.remove());
        document.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));
        
        // Display new errors
        for (const [field, fieldErrors] of Object.entries(errors)) {
            const fieldElement = document.querySelector(`[name="${field}"]`);
            if (fieldElement) {
                fieldElement.classList.add('is-invalid');
                
                const errorDiv = document.createElement('div');
                errorDiv.className = 'invalid-feedback';
                errorDiv.textContent = fieldErrors.join(', ');
                
                fieldElement.parentNode.appendChild(errorDiv);
                
                // Switch to tab containing the error field
                const tabPane = fieldElement.closest('.tab-pane');
                if (tabPane) {
                    const tabId = tabPane.id;
                    const tabLink = document.querySelector(`[href="#${tabId}"]`);
                    if (tabLink) {
                        new bootstrap.Tab(tabLink).show();
                    }
                }
            }
        }
    }
    
    // Phone number masking
    document.querySelectorAll('[data-mask]').forEach(input => {
        input.addEventListener('input', function() {
            let value = this.value.replace(/\D/g, '');
            if (value.startsWith('212')) {
                value = value.substring(3);
            }
            if (value.length > 0) {
                value = '+212-' + value.replace(/(\d{3})(\d{3})(\d{3})/, '$1-$2-$3');
            }
            this.value = value;
        });
    });
    
    // Auto-save draft (optional)
    let autoSaveTimer;
    form.addEventListener('input', function() {
        clearTimeout(autoSaveTimer);
        autoSaveTimer = setTimeout(() => {
            // Save form data to localStorage
            const formData = new FormData(form);
            const data = {};
            for (let [key, value] of formData.entries()) {
                data[key] = value;
            }
            localStorage.setItem('client_form_draft', JSON.stringify(data));
        }, 2000);
    });
    
    // Load draft on page load
    const draft = localStorage.getItem('client_form_draft');
    if (draft && !form.querySelector('[name="id"]').value) {
        const data = JSON.parse(draft);
        for (const [key, value] of Object.entries(data)) {
            const field = form.querySelector(`[name="${key}"]`);
            if (field) {
                field.value = value;
            }
        }
    }
    
    // Clear draft on successful submit
    form.addEventListener('submit', function() {
        localStorage.removeItem('client_form_draft');
    });
    
    // Initialize progress
    updateProgress();
});
</script>
{% endblock %}
