"""
External service integration management.
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from apps.core.models import TimeStampedModel


class ExternalService(TimeStampedModel):
    """External services and APIs."""
    
    SERVICE_TYPES = [
        ('payment', _('بوابة دفع')),
        ('sms', _('خدمة رسائل نصية')),
        ('email', _('خدمة بريد إلكتروني')),
        ('whatsapp', _('واتساب')),
        ('maps', _('خرائط')),
        ('weather', _('طقس')),
        ('currency', _('أسعار صرف')),
        ('airline', _('خطوط طيران')),
        ('hotel', _('فنادق')),
        ('car_rental', _('تأجير سيارات')),
        ('visa', _('تأشيرات')),
        ('insurance', _('تأمين')),
        ('analytics', _('تحليلات')),
        ('crm', _('إدارة علاقات العملاء')),
        ('accounting', _('محاسبة')),
        ('backup', _('نسخ احتياطية')),
        ('other', _('أخرى')),
    ]
    
    STATUS_CHOICES = [
        ('active', _('نشط')),
        ('inactive', _('غير نشط')),
        ('testing', _('قيد الاختبار')),
        ('error', _('خطأ')),
        ('maintenance', _('صيانة')),
    ]
    
    # Basic Information
    name = models.CharField(_('اسم الخدمة'), max_length=100)
    description = models.TextField(_('الوصف'), blank=True)
    service_type = models.CharField(_('نوع الخدمة'), max_length=20, choices=SERVICE_TYPES)
    provider = models.CharField(_('مقدم الخدمة'), max_length=100)
    
    # Connection Details
    base_url = models.URLField(_('الرابط الأساسي'), blank=True)
    api_version = models.CharField(_('إصدار API'), max_length=20, blank=True)
    
    # Authentication
    api_key = models.CharField(_('مفتاح API'), max_length=500, blank=True)
    secret_key = models.CharField(_('المفتاح السري'), max_length=500, blank=True)
    username = models.CharField(_('اسم المستخدم'), max_length=100, blank=True)
    password = models.CharField(_('كلمة المرور'), max_length=200, blank=True)
    token = models.TextField(_('الرمز المميز'), blank=True)
    
    # Configuration
    configuration = models.JSONField(_('التكوين'), default=dict, blank=True)
    headers = models.JSONField(_('رؤوس HTTP'), default=dict, blank=True)
    
    # Rate Limiting
    rate_limit_per_minute = models.PositiveIntegerField(_('حد المعدل في الدقيقة'), null=True, blank=True)
    rate_limit_per_hour = models.PositiveIntegerField(_('حد المعدل في الساعة'), null=True, blank=True)
    rate_limit_per_day = models.PositiveIntegerField(_('حد المعدل في اليوم'), null=True, blank=True)
    
    # Status and Health
    status = models.CharField(_('الحالة'), max_length=20, choices=STATUS_CHOICES, default='inactive')
    last_health_check = models.DateTimeField(_('آخر فحص صحة'), null=True, blank=True)
    health_check_interval_minutes = models.PositiveIntegerField(_('فترة فحص الصحة (دقائق)'), default=60)
    
    # Statistics
    total_requests = models.PositiveIntegerField(_('إجمالي الطلبات'), default=0)
    successful_requests = models.PositiveIntegerField(_('الطلبات الناجحة'), default=0)
    failed_requests = models.PositiveIntegerField(_('الطلبات الفاشلة'), default=0)
    
    # Costs
    cost_per_request = models.DecimalField(_('تكلفة الطلب'), max_digits=10, decimal_places=4, null=True, blank=True)
    monthly_cost = models.DecimalField(_('التكلفة الشهرية'), max_digits=10, decimal_places=2, null=True, blank=True)
    
    # Settings
    is_enabled = models.BooleanField(_('مفعل'), default=False)
    is_sandbox = models.BooleanField(_('بيئة اختبار'), default=True)
    timeout_seconds = models.PositiveIntegerField(_('انتهاء الوقت (ثانية)'), default=30)
    retry_attempts = models.PositiveIntegerField(_('محاولات الإعادة'), default=3)
    
    notes = models.TextField(_('ملاحظات'), blank=True)
    
    class Meta:
        verbose_name = _('خدمة خارجية')
        verbose_name_plural = _('الخدمات الخارجية')
        ordering = ['service_type', 'name']
    
    def __str__(self):
        return f"{self.name} ({self.get_service_type_display()})"
    
    @property
    def success_rate(self):
        """Calculate success rate percentage."""
        if self.total_requests > 0:
            return (self.successful_requests / self.total_requests) * 100
        return 0
    
    @property
    def is_healthy(self):
        """Check if service is healthy based on recent checks."""
        return self.status == 'active'


class APICall(TimeStampedModel):
    """Log of API calls to external services."""
    
    STATUS_CHOICES = [
        ('pending', _('معلق')),
        ('success', _('نجح')),
        ('failed', _('فشل')),
        ('timeout', _('انتهت المهلة')),
        ('rate_limited', _('محدود المعدل')),
    ]
    
    service = models.ForeignKey(
        ExternalService,
        on_delete=models.CASCADE,
        related_name='api_calls',
        verbose_name=_('الخدمة')
    )
    
    # Request Details
    endpoint = models.CharField(_('نقطة النهاية'), max_length=500)
    method = models.CharField(_('الطريقة'), max_length=10, default='GET')
    request_headers = models.JSONField(_('رؤوس الطلب'), default=dict, blank=True)
    request_body = models.TextField(_('محتوى الطلب'), blank=True)
    
    # Response Details
    status_code = models.PositiveIntegerField(_('رمز الحالة'), null=True, blank=True)
    response_headers = models.JSONField(_('رؤوس الاستجابة'), default=dict, blank=True)
    response_body = models.TextField(_('محتوى الاستجابة'), blank=True)
    
    # Timing
    request_time = models.DateTimeField(_('وقت الطلب'), auto_now_add=True)
    response_time = models.DateTimeField(_('وقت الاستجابة'), null=True, blank=True)
    duration_ms = models.PositiveIntegerField(_('المدة (ميلي ثانية)'), null=True, blank=True)
    
    # Status
    status = models.CharField(_('الحالة'), max_length=20, choices=STATUS_CHOICES, default='pending')
    error_message = models.TextField(_('رسالة الخطأ'), blank=True)
    
    # Context
    user = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('المستخدم')
    )
    ip_address = models.GenericIPAddressField(_('عنوان IP'), null=True, blank=True)
    user_agent = models.TextField(_('معلومات المتصفح'), blank=True)
    
    # Cost
    cost = models.DecimalField(_('التكلفة'), max_digits=10, decimal_places=4, null=True, blank=True)
    
    class Meta:
        verbose_name = _('استدعاء API')
        verbose_name_plural = _('استدعاءات API')
        ordering = ['-request_time']
        indexes = [
            models.Index(fields=['service', 'status']),
            models.Index(fields=['request_time']),
            models.Index(fields=['status_code']),
        ]
    
    def __str__(self):
        return f"{self.service.name} - {self.method} {self.endpoint}"


class WebhookEndpoint(TimeStampedModel):
    """Webhook endpoints for receiving data from external services."""
    
    STATUS_CHOICES = [
        ('active', _('نشط')),
        ('inactive', _('غير نشط')),
        ('paused', _('متوقف')),
    ]
    
    service = models.ForeignKey(
        ExternalService,
        on_delete=models.CASCADE,
        related_name='webhooks',
        verbose_name=_('الخدمة')
    )
    
    # Endpoint Details
    name = models.CharField(_('اسم النقطة'), max_length=100)
    url_path = models.CharField(_('مسار URL'), max_length=200, unique=True)
    description = models.TextField(_('الوصف'), blank=True)
    
    # Security
    secret_key = models.CharField(_('المفتاح السري'), max_length=200, blank=True)
    allowed_ips = models.JSONField(_('عناوين IP المسموحة'), default=list, blank=True)
    
    # Processing
    handler_function = models.CharField(_('دالة المعالجة'), max_length=200)
    is_async = models.BooleanField(_('غير متزامن'), default=True)
    
    # Status
    status = models.CharField(_('الحالة'), max_length=20, choices=STATUS_CHOICES, default='active')
    
    # Statistics
    total_calls = models.PositiveIntegerField(_('إجمالي الاستدعاءات'), default=0)
    successful_calls = models.PositiveIntegerField(_('الاستدعاءات الناجحة'), default=0)
    failed_calls = models.PositiveIntegerField(_('الاستدعاءات الفاشلة'), default=0)
    
    last_called = models.DateTimeField(_('آخر استدعاء'), null=True, blank=True)
    
    class Meta:
        verbose_name = _('نقطة Webhook')
        verbose_name_plural = _('نقاط Webhook')
        ordering = ['service', 'name']
    
    def __str__(self):
        return f"{self.service.name} - {self.name}"


class WebhookCall(TimeStampedModel):
    """Log of webhook calls received."""
    
    STATUS_CHOICES = [
        ('received', _('مستلم')),
        ('processing', _('قيد المعالجة')),
        ('processed', _('معالج')),
        ('failed', _('فشل')),
        ('ignored', _('متجاهل')),
    ]
    
    webhook = models.ForeignKey(
        WebhookEndpoint,
        on_delete=models.CASCADE,
        related_name='calls',
        verbose_name=_('نقطة Webhook')
    )
    
    # Request Details
    headers = models.JSONField(_('الرؤوس'), default=dict, blank=True)
    body = models.TextField(_('المحتوى'))
    method = models.CharField(_('الطريقة'), max_length=10, default='POST')
    
    # Source
    source_ip = models.GenericIPAddressField(_('عنوان IP المصدر'))
    user_agent = models.TextField(_('معلومات المتصفح'), blank=True)
    
    # Processing
    status = models.CharField(_('الحالة'), max_length=20, choices=STATUS_CHOICES, default='received')
    processed_at = models.DateTimeField(_('معالج في'), null=True, blank=True)
    processing_time_ms = models.PositiveIntegerField(_('وقت المعالجة (ميلي ثانية)'), null=True, blank=True)
    
    # Results
    response_status = models.PositiveIntegerField(_('حالة الاستجابة'), default=200)
    response_body = models.TextField(_('محتوى الاستجابة'), blank=True)
    error_message = models.TextField(_('رسالة الخطأ'), blank=True)
    
    # Verification
    signature_verified = models.BooleanField(_('التوقيع موثق'), default=False)
    
    class Meta:
        verbose_name = _('استدعاء Webhook')
        verbose_name_plural = _('استدعاءات Webhook')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['webhook', 'status']),
            models.Index(fields=['created_at']),
            models.Index(fields=['source_ip']),
        ]
    
    def __str__(self):
        return f"{self.webhook.name} - {self.created_at}"


class SyncConfiguration(TimeStampedModel):
    """Configuration for data synchronization with external services."""
    
    SYNC_DIRECTIONS = [
        ('import', _('استيراد')),
        ('export', _('تصدير')),
        ('bidirectional', _('ثنائي الاتجاه')),
    ]
    
    SYNC_FREQUENCIES = [
        ('real_time', _('فوري')),
        ('hourly', _('كل ساعة')),
        ('daily', _('يومي')),
        ('weekly', _('أسبوعي')),
        ('manual', _('يدوي')),
    ]
    
    service = models.ForeignKey(
        ExternalService,
        on_delete=models.CASCADE,
        related_name='sync_configs',
        verbose_name=_('الخدمة')
    )
    
    # Configuration
    name = models.CharField(_('اسم التزامن'), max_length=100)
    description = models.TextField(_('الوصف'), blank=True)
    
    # Data Mapping
    local_model = models.CharField(_('النموذج المحلي'), max_length=100)
    remote_endpoint = models.CharField(_('نقطة النهاية البعيدة'), max_length=500)
    field_mapping = models.JSONField(_('تخطيط الحقول'), default=dict)
    
    # Sync Settings
    direction = models.CharField(_('الاتجاه'), max_length=20, choices=SYNC_DIRECTIONS)
    frequency = models.CharField(_('التكرار'), max_length=20, choices=SYNC_FREQUENCIES)
    
    # Filters and Conditions
    sync_conditions = models.JSONField(_('شروط التزامن'), default=dict, blank=True)
    last_sync_timestamp = models.DateTimeField(_('آخر تزامن'), null=True, blank=True)
    
    # Status
    is_enabled = models.BooleanField(_('مفعل'), default=False)
    
    # Error Handling
    on_error_action = models.CharField(
        _('إجراء عند الخطأ'),
        max_length=20,
        choices=[
            ('stop', _('توقف')),
            ('skip', _('تخطي')),
            ('retry', _('إعادة المحاولة')),
        ],
        default='stop'
    )
    max_retries = models.PositiveIntegerField(_('أقصى محاولات'), default=3)
    
    class Meta:
        verbose_name = _('تكوين التزامن')
        verbose_name_plural = _('تكوينات التزامن')
        ordering = ['service', 'name']
    
    def __str__(self):
        return f"{self.service.name} - {self.name}"
